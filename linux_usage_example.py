#!/usr/bin/env python3
"""
Linux usage example for CallTag Controller
Linux系统使用示例

This example demonstrates how to use the CallTag Controller library on Linux
to control sound-light tag readers.
"""

import time
import sys
import signal
import threading
from typing import List

# Import the CallTag Controller library
from calltag_controller import (
    CallTagDevice, 
    SerialConnection, 
    USBConnection, 
    TCPClientConnection,
    TCPServerConnection
)
from calltag_controller.core.protocol import Protocol
from calltag_controller.core.tag_info import TagInfo


class CallTagManager:
    """
    Manager class for CallTag devices on Linux
    Linux系统下的声光标签设备管理器
    """
    
    def __init__(self):
        self.device = None
        self.connection = None
        self.running = False
        self.detected_tags: List[TagInfo] = []
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\nReceived signal {signum}, shutting down...")
        self.shutdown()
        sys.exit(0)
    
    def setup_serial_connection(self, port='/dev/ttyUSB0', baudrate=115200):
        """
        Set up serial connection (most common on Linux)
        设置串口连接（Linux上最常用）
        """
        print(f"Setting up serial connection on {port}@{baudrate}")
        
        # Check available ports
        temp_conn = SerialConnection(port, baudrate)
        available_ports = temp_conn.available_ports()
        print(f"Available serial ports: {available_ports}")
        
        if not available_ports:
            print("No serial ports found!")
            return False
        
        # Use specified port or first available
        if port not in available_ports:
            print(f"Port {port} not available, using {available_ports[0]}")
            port = available_ports[0]
        
        self.connection = SerialConnection(port, baudrate)
        return True
    
    def setup_usb_connection(self):
        """
        Set up USB HID connection
        设置USB HID连接
        """
        print("Setting up USB HID connection")
        
        self.connection = USBConnection()
        
        # Check available devices
        devices = self.connection.get_available_devices()
        print(f"Found {len(devices)} USB HID devices:")
        for i, dev in enumerate(devices):
            print(f"  {i}: VID=0x{dev['vendor_id']:04X}, PID=0x{dev['product_id']:04X}")
            print(f"      Product: {dev.get('product_string', 'Unknown')}")
            print(f"      Serial: {dev.get('serial_number', 'Unknown')}")
        
        return len(devices) > 0
    
    def setup_tcp_client(self, host='*************', port=60000):
        """
        Set up TCP client connection
        设置TCP客户端连接
        """
        print(f"Setting up TCP client connection to {host}:{port}")
        self.connection = TCPClientConnection(host, port)
        return True
    
    def setup_device(self):
        """
        Set up the CallTag device with callbacks
        设置声光标签设备和回调函数
        """
        if not self.connection:
            print("No connection configured!")
            return False
        
        self.device = CallTagDevice(self.connection)
        
        # Set up callbacks
        self.device.on_tag_detected = self.on_tag_detected
        self.device.on_error = self.on_error
        self.device.on_connection_lost = self.on_connection_lost
        
        return True
    
    def on_tag_detected(self, tag_info: TagInfo):
        """Callback when a tag is detected"""
        print(f"[TAG] Detected: ID={tag_info.id}, State=0x{tag_info.state:02X}, Count={tag_info.num}")
        
        # Add to our list if not already present
        existing = next((t for t in self.detected_tags if t.id == tag_info.id), None)
        if existing:
            existing.num = tag_info.num
            existing.state = tag_info.state
        else:
            self.detected_tags.append(tag_info)
    
    def on_error(self, error_msg: str):
        """Callback for errors"""
        print(f"[ERROR] {error_msg}")
    
    def on_connection_lost(self):
        """Callback when connection is lost"""
        print("[WARNING] Connection lost!")
        self.running = False
    
    def connect(self):
        """Connect to the device"""
        if not self.device:
            print("Device not configured!")
            return False
        
        print("Connecting to device...")
        if self.device.connect():
            print("Connected successfully!")
            
            # Get device info
            info = self.device.get_device_info()
            print(f"Device info: {info}")
            
            return True
        else:
            print("Failed to connect!")
            return False
    
    def start_monitoring(self):
        """Start monitoring for tags"""
        if not self.device or not self.device.is_connected:
            print("Device not connected!")
            return False
        
        print("Starting tag monitoring...")
        if self.device.start_reading():
            print("Monitoring started successfully!")
            self.running = True
            return True
        else:
            print("Failed to start monitoring!")
            return False
    
    def stop_monitoring(self):
        """Stop monitoring for tags"""
        if self.device:
            print("Stopping tag monitoring...")
            self.device.stop_reading()
            self.running = False
            print("Monitoring stopped")
    
    def call_tag(self, tag_id: int, duration: int = Protocol.TIME_30_SEC):
        """
        Call a specific tag
        呼叫指定标签
        """
        if not self.device or not self.device.is_connected:
            print("Device not connected!")
            return False
        
        print(f"Calling tag {tag_id}...")
        success = self.device.call_tag(
            tag_id, 
            Protocol.LED_FLASH_MODE,  # Flash mode
            duration,                 # Duration
            True                      # Enable buzzer
        )
        
        if success:
            print(f"Tag {tag_id} called successfully!")
        else:
            print(f"Failed to call tag {tag_id}")
        
        return success
    
    def stop_call_tag(self, tag_id: int):
        """
        Stop calling a specific tag
        停止呼叫指定标签
        """
        if not self.device or not self.device.is_connected:
            print("Device not connected!")
            return False
        
        print(f"Stopping call for tag {tag_id}...")
        success = self.device.stop_call_tag(tag_id)
        
        if success:
            print(f"Stopped calling tag {tag_id}")
        else:
            print(f"Failed to stop calling tag {tag_id}")
        
        return success
    
    def show_detected_tags(self):
        """Show all detected tags"""
        print(f"\nDetected tags ({len(self.detected_tags)}):")
        if self.detected_tags:
            for tag in self.detected_tags:
                print(f"  ID: {tag.id}, State: 0x{tag.state:02X}, Count: {tag.num}")
        else:
            print("  No tags detected yet")
        print()
    
    def interactive_mode(self):
        """Run in interactive mode"""
        print("\nInteractive mode. Available commands:")
        print("  'tags'        - Show detected tags")
        print("  'call <id>'   - Call tag with specified ID")
        print("  'stop <id>'   - Stop calling tag with specified ID")
        print("  'clear'       - Clear detected tags")
        print("  'status'      - Show device status")
        print("  'quit'        - Exit")
        print()
        
        while self.running:
            try:
                cmd = input("CallTag> ").strip().split()
                
                if not cmd:
                    continue
                
                command = cmd[0].lower()
                
                if command == 'quit':
                    break
                elif command == 'tags':
                    self.show_detected_tags()
                elif command == 'call':
                    if len(cmd) > 1:
                        try:
                            tag_id = int(cmd[1])
                            self.call_tag(tag_id)
                        except ValueError:
                            print("Invalid tag ID. Please enter a number.")
                    else:
                        print("Usage: call <tag_id>")
                elif command == 'stop':
                    if len(cmd) > 1:
                        try:
                            tag_id = int(cmd[1])
                            self.stop_call_tag(tag_id)
                        except ValueError:
                            print("Invalid tag ID. Please enter a number.")
                    else:
                        print("Usage: stop <tag_id>")
                elif command == 'clear':
                    self.detected_tags.clear()
                    if self.device:
                        self.device.clear_cache()
                    print("Tag cache cleared")
                elif command == 'status':
                    print(f"Device connected: {self.device.is_connected if self.device else False}")
                    print(f"Monitoring: {self.running}")
                    print(f"Tags detected: {len(self.detected_tags)}")
                elif command == 'help':
                    print("Available commands: tags, call, stop, clear, status, quit")
                else:
                    print(f"Unknown command: {command}")
            
            except EOFError:
                break
            except KeyboardInterrupt:
                print("\nUse 'quit' to exit")
    
    def shutdown(self):
        """Shutdown the manager"""
        print("Shutting down CallTag Manager...")
        self.stop_monitoring()
        if self.device:
            self.device.disconnect()
        print("Shutdown complete")


def main():
    """Main function"""
    print("CallTag Controller - Linux Usage Example")
    print("=" * 50)
    
    manager = CallTagManager()
    
    # Choose connection type
    print("Choose connection type:")
    print("1. Serial (recommended for Linux)")
    print("2. USB HID")
    print("3. TCP Client")
    
    try:
        choice = input("Enter choice (1-3): ").strip()
        
        if choice == '1':
            # Serial connection
            port = input("Enter serial port (default: /dev/ttyUSB0): ").strip()
            if not port:
                port = '/dev/ttyUSB0'
            
            if not manager.setup_serial_connection(port):
                print("Failed to setup serial connection")
                return 1
        
        elif choice == '2':
            # USB HID connection
            if not manager.setup_usb_connection():
                print("No USB devices found")
                return 1
        
        elif choice == '3':
            # TCP client connection
            host = input("Enter server IP (default: *************): ").strip()
            if not host:
                host = '*************'
            
            port_str = input("Enter server port (default: 60000): ").strip()
            port = 60000 if not port_str else int(port_str)
            
            manager.setup_tcp_client(host, port)
        
        else:
            print("Invalid choice")
            return 1
        
        # Set up device
        if not manager.setup_device():
            return 1
        
        # Connect
        if not manager.connect():
            return 1
        
        # Start monitoring
        if not manager.start_monitoring():
            return 1
        
        # Run interactive mode
        manager.interactive_mode()
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    finally:
        manager.shutdown()
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
