"""
Helper functions for CallTag Controller
辅助函数
"""

import re
import socket
from typing import Union, Optional


def format_hex(data: bytes, separator: str = ' ') -> str:
    """
    Format bytes as hexadecimal string
    将字节格式化为十六进制字符串
    
    Args:
        data (bytes): Data to format
        separator (str): Separator between hex values
        
    Returns:
        str: Formatted hex string
    """
    return separator.join(f'{b:02X}' for b in data)


def parse_device_id(id_str: str) -> Optional[int]:
    """
    Parse device ID from string (supports both hex and decimal)
    从字符串解析设备ID（支持十六进制和十进制）
    
    Args:
        id_str (str): ID string to parse
        
    Returns:
        Optional[int]: Parsed ID or None if invalid
    """
    try:
        # Try hex first (if starts with 0x or contains A-F)
        if id_str.startswith('0x') or id_str.startswith('0X'):
            return int(id_str, 16)
        elif any(c in id_str.upper() for c in 'ABCDEF'):
            return int(id_str, 16)
        else:
            # Try decimal
            return int(id_str, 10)
    except ValueError:
        return None


def validate_ip_address(ip: str) -> bool:
    """
    Validate IP address format
    验证IP地址格式
    
    Args:
        ip (str): IP address to validate
        
    Returns:
        bool: True if valid IP address
    """
    try:
        socket.inet_aton(ip)
        return True
    except socket.error:
        return False


def validate_port(port: Union[str, int]) -> bool:
    """
    Validate port number
    验证端口号
    
    Args:
        port (Union[str, int]): Port to validate
        
    Returns:
        bool: True if valid port number
    """
    try:
        port_num = int(port)
        return 1 <= port_num <= 65535
    except (ValueError, TypeError):
        return False


def bytes_to_int(data: bytes, byteorder: str = 'big') -> int:
    """
    Convert bytes to integer
    将字节转换为整数
    
    Args:
        data (bytes): Bytes to convert
        byteorder (str): Byte order ('big' or 'little')
        
    Returns:
        int: Converted integer
    """
    return int.from_bytes(data, byteorder=byteorder)


def int_to_bytes(value: int, length: int, byteorder: str = 'big') -> bytes:
    """
    Convert integer to bytes
    将整数转换为字节
    
    Args:
        value (int): Integer to convert
        length (int): Number of bytes
        byteorder (str): Byte order ('big' or 'little')
        
    Returns:
        bytes: Converted bytes
    """
    return value.to_bytes(length, byteorder=byteorder)


def calculate_timeout(data_size: int, baudrate: int = 115200) -> float:
    """
    Calculate appropriate timeout for data transmission
    计算数据传输的适当超时时间
    
    Args:
        data_size (int): Size of data in bytes
        baudrate (int): Transmission baud rate
        
    Returns:
        float: Timeout in seconds
    """
    # Calculate transmission time and add some margin
    bits_per_byte = 10  # 8 data + 1 start + 1 stop
    transmission_time = (data_size * bits_per_byte) / baudrate
    return max(transmission_time * 2, 0.1)  # At least 100ms


def merge_tag_lists(old_tags: list, new_tags: list) -> list:
    """
    Merge two tag lists, updating counts for existing tags
    合并两个标签列表，更新现有标签的计数
    
    Args:
        old_tags (list): Existing tag list
        new_tags (list): New tag list
        
    Returns:
        list: Merged tag list
    """
    # Create a dictionary for quick lookup
    tag_dict = {tag.id: tag for tag in old_tags}
    
    # Process new tags
    for new_tag in new_tags:
        if new_tag.id in tag_dict:
            # Update existing tag
            tag_dict[new_tag.id].increment_count()
            tag_dict[new_tag.id].update_state(new_tag.state)
        else:
            # Add new tag
            tag_dict[new_tag.id] = new_tag
    
    return list(tag_dict.values())


def format_device_info(info: dict) -> str:
    """
    Format device information for display
    格式化设备信息用于显示
    
    Args:
        info (dict): Device information dictionary
        
    Returns:
        str: Formatted device information
    """
    lines = []
    for key, value in info.items():
        formatted_key = key.replace('_', ' ').title()
        lines.append(f"{formatted_key}: {value}")
    return '\n'.join(lines)


def create_json_response(dev_sn: str, tags: list) -> str:
    """
    Create JSON response in the format expected by the original system
    创建原系统期望格式的JSON响应
    
    Args:
        dev_sn (str): Device serial number
        tags (list): List of TagInfo objects
        
    Returns:
        str: JSON string
    """
    import json
    
    attence_list = []
    for tag in tags:
        attence_list.append({
            'ID': f"{int(tag.id):08X}" if tag.id.isdigit() else tag.id,
            'State': f"{tag.state:02X}"
        })
    
    response = {
        'DevSN': dev_sn,
        'AttenceID': attence_list
    }
    
    return json.dumps(response)
