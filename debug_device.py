#!/usr/bin/env python3
"""
Debug script for CallTag device communication
设备通信调试脚本
"""

import time
import sys
from calltag_controller.connections.tcp_client import TCPClientConnection
from calltag_controller.core.protocol import Protocol


class DeviceDebugger:
    """Device communication debugger"""
    
    def __init__(self, host="*************", port=60000):
        self.host = host
        self.port = port
        self.connection = None
    
    def connect(self):
        """Connect to device"""
        print(f"Connecting to {self.host}:{self.port}...")
        self.connection = TCPClientConnection(self.host, self.port, timeout=10.0)
        
        if self.connection.connect():
            print("✅ Connected successfully!")
            return True
        else:
            print("❌ Connection failed!")
            return False
    
    def send_command(self, command_bytes, description=""):
        """Send command and show response"""
        if not self.connection or not self.connection.is_connected:
            print("❌ Not connected!")
            return None
        
        print(f"\n📤 Sending {description}: {command_bytes.hex()}")
        
        if self.connection.send(command_bytes):
            print("✅ Command sent successfully")
            
            # Wait for response
            time.sleep(0.5)
            response = self.connection.receive()
            
            if response:
                print(f"📥 Received response ({len(response)} bytes): {response.hex()}")
                
                # Try to decode as text
                try:
                    text = response.decode('utf-8', errors='ignore')
                    if text.strip():
                        print(f"📄 As text: {text}")
                except:
                    pass
                
                # Try to parse as protocol
                success, parsed = Protocol.parse_response(response)
                if success:
                    print(f"📋 Parsed: Address={parsed['address']}, Command=0x{parsed['command']:02X}")
                    if parsed['payload']:
                        print(f"📦 Payload: {parsed['payload'].hex()}")
                
                return response
            else:
                print("📭 No response received")
                return None
        else:
            print("❌ Failed to send command")
            return None
    
    def test_basic_commands(self):
        """Test basic protocol commands"""
        print("\n=== Testing Basic Commands ===")
        
        commands = [
            (Protocol.create_start_read_command(1), "Start Read"),
            (Protocol.create_inquiry_command(1), "Inquiry"),
            (Protocol.create_stop_read_command(1), "Stop Read"),
        ]
        
        for cmd, desc in commands:
            response = self.send_command(cmd, desc)
            time.sleep(1)  # Wait between commands
    
    def test_continuous_inquiry(self, duration=10):
        """Test continuous inquiry like the original software"""
        print(f"\n=== Testing Continuous Inquiry ({duration} seconds) ===")
        
        # First send start read
        start_cmd = Protocol.create_start_read_command(1)
        self.send_command(start_cmd, "Start Read")
        time.sleep(1)
        
        # Then continuously send inquiry commands
        end_time = time.time() + duration
        inquiry_count = 0
        
        while time.time() < end_time:
            inquiry_cmd = Protocol.create_inquiry_command(1)
            response = self.send_command(inquiry_cmd, f"Inquiry #{inquiry_count + 1}")
            inquiry_count += 1
            
            # Check if we got tag data
            if response and len(response) > 10:
                try:
                    # Try to find JSON data
                    text = response.decode('utf-8', errors='ignore')
                    if 'AttenceID' in text:
                        print("🏷️  Found tag data in response!")
                        
                        # Parse tags
                        tags = Protocol.parse_tag_data(text)
                        if tags:
                            print(f"📊 Parsed {len(tags)} tags:")
                            for tag in tags:
                                print(f"   • {tag}")
                except:
                    pass
            
            time.sleep(0.5)  # Wait 500ms between inquiries
        
        # Send stop read
        stop_cmd = Protocol.create_stop_read_command(1)
        self.send_command(stop_cmd, "Stop Read")
        
        print(f"📈 Sent {inquiry_count} inquiry commands")
    
    def test_call_command(self, tag_id=123456):
        """Test calling a specific tag"""
        print(f"\n=== Testing Call Command (Tag ID: {tag_id}) ===")
        
        # Call tag
        call_cmd = Protocol.create_call_tag_command(
            1, tag_id, 
            Protocol.LED_FLASH_MODE, 
            Protocol.TIME_30_SEC, 
            True
        )
        response = self.send_command(call_cmd, f"Call Tag {tag_id}")
        
        time.sleep(3)  # Wait 3 seconds
        
        # Stop call
        stop_cmd = Protocol.create_stop_call_command(1, tag_id)
        self.send_command(stop_cmd, f"Stop Call Tag {tag_id}")
    
    def raw_communication_test(self):
        """Test raw communication without protocol"""
        print("\n=== Raw Communication Test ===")
        
        # Send some raw data to see what happens
        raw_commands = [
            b'SW\x00\x03\x01\x41\x11',  # Start read
            b'SW\x00\x03\x01\x43\x0F',  # Inquiry
            b'Hello',                    # Invalid command
        ]
        
        for i, cmd in enumerate(raw_commands):
            print(f"\n📤 Sending raw command {i+1}: {cmd.hex()}")
            if self.connection.send(cmd):
                time.sleep(0.5)
                response = self.connection.receive()
                if response:
                    print(f"📥 Raw response: {response.hex()}")
                    try:
                        text = response.decode('utf-8', errors='ignore')
                        if text.strip():
                            print(f"📄 As text: {text}")
                    except:
                        pass
                else:
                    print("📭 No response")
            time.sleep(1)
    
    def disconnect(self):
        """Disconnect from device"""
        if self.connection:
            self.connection.disconnect()
            print("🔌 Disconnected")


def main():
    """Main function"""
    print("CallTag Device Communication Debugger")
    print("=" * 40)
    
    debugger = DeviceDebugger()
    
    try:
        if not debugger.connect():
            return 1
        
        print("\nChoose test:")
        print("1. Basic commands test")
        print("2. Continuous inquiry test")
        print("3. Call command test")
        print("4. Raw communication test")
        print("5. All tests")
        
        choice = input("Enter choice (1-5): ").strip()
        
        if choice == '1':
            debugger.test_basic_commands()
        elif choice == '2':
            debugger.test_continuous_inquiry()
        elif choice == '3':
            debugger.test_call_command()
        elif choice == '4':
            debugger.raw_communication_test()
        elif choice == '5':
            debugger.test_basic_commands()
            debugger.test_continuous_inquiry()
            debugger.test_call_command()
            debugger.raw_communication_test()
        else:
            print("Invalid choice")
            return 1
    
    except KeyboardInterrupt:
        print("\n\nTest interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        debugger.disconnect()
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
