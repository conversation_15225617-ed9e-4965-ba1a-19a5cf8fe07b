#!/usr/bin/env python3
"""
Test script for CallTag Controller
测试脚本
"""

import sys
import time
from calltag_controller.core.protocol import Protocol
from calltag_controller.core.tag_info import TagInfo
from calltag_controller.utils.helpers import *


def test_protocol():
    """Test protocol functions"""
    print("=== Testing Protocol ===")
    
    # Test checksum calculation
    data = b'SWTEST'
    checksum = Protocol.calculate_checksum(data)
    print(f"Checksum for {data}: 0x{checksum:02X}")
    
    # Test command packet creation
    cmd_packet = Protocol.create_start_read_command(1)
    print(f"Start read command: {format_hex(cmd_packet)}")
    
    cmd_packet = Protocol.create_inquiry_command(1)
    print(f"Inquiry command: {format_hex(cmd_packet)}")
    
    cmd_packet = Protocol.create_call_tag_command(1, 123456, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True)
    print(f"Call tag command: {format_hex(cmd_packet)}")
    
    # Test response parsing
    test_response = b'SW\x00\x05\x01\x43\x00\x00\xB9'
    success, parsed = Protocol.parse_response(test_response)
    print(f"Parse response success: {success}")
    if success:
        print(f"Parsed data: {parsed}")
    
    print()


def test_tag_info():
    """Test TagInfo class"""
    print("=== Testing TagInfo ===")
    
    # Create tag
    tag = TagInfo("123456", 0x01, 1, "TestDevice")
    print(f"Created tag: {tag}")
    
    # Test methods
    tag.increment_count()
    print(f"After increment: {tag}")
    
    tag.update_state(0xFF)
    print(f"After state update: {tag}")
    
    # Test serialization
    tag_dict = tag.to_dict()
    print(f"Tag as dict: {tag_dict}")
    
    tag_json = tag.to_json()
    print(f"Tag as JSON: {tag_json}")
    
    # Test deserialization
    tag2 = TagInfo.from_json(tag_json)
    print(f"Tag from JSON: {tag2}")
    
    print()


def test_helpers():
    """Test helper functions"""
    print("=== Testing Helpers ===")
    
    # Test hex formatting
    data = b'\x01\x02\x03\xFF'
    hex_str = format_hex(data)
    print(f"Hex format: {hex_str}")
    
    # Test ID parsing
    test_ids = ['123456', '0x1E240', '1E240', 'invalid']
    for id_str in test_ids:
        parsed_id = parse_device_id(id_str)
        print(f"Parse '{id_str}': {parsed_id}")
    
    # Test IP validation
    test_ips = ['***********', '127.0.0.1', '256.1.1.1', 'invalid']
    for ip in test_ips:
        valid = validate_ip_address(ip)
        print(f"IP '{ip}' valid: {valid}")
    
    # Test port validation
    test_ports = [80, 65535, 0, 65536, 'invalid']
    for port in test_ports:
        valid = validate_port(port)
        print(f"Port '{port}' valid: {valid}")
    
    print()


def test_json_parsing():
    """Test JSON parsing functionality"""
    print("=== Testing JSON Parsing ===")
    
    # Test JSON data similar to what the device sends
    test_json = '''
    {
        "DevSN": "FF24062720B6",
        "AttenceID": [
            {"ID": "0001E240", "State": "01"},
            {"ID": "000C0FFE", "State": "02"},
            {"ID": "00123456", "State": "01"}
        ]
    }
    '''
    
    tags = Protocol.parse_tag_data(test_json)
    print(f"Parsed {len(tags)} tags from JSON:")
    for tag in tags:
        print(f"  {tag}")
    
    # Test creating JSON response
    json_response = create_json_response("FF24062720B6", tags)
    print(f"Created JSON response: {json_response}")
    
    print()


def test_connections():
    """Test connection classes (without actual hardware)"""
    print("=== Testing Connections ===")
    
    # Test serial connection (will fail without hardware, but tests class creation)
    try:
        from calltag_controller.connections.serial_conn import SerialConnection
        serial_conn = SerialConnection('/dev/ttyUSB0', 115200)
        print(f"Serial connection created: {serial_conn.port}@{serial_conn.baudrate}")
        
        # Test available ports
        ports = serial_conn.available_ports()
        print(f"Available serial ports: {ports}")
    except ImportError as e:
        print(f"Serial connection test skipped: {e}")
    
    # Test USB connection
    try:
        from calltag_controller.connections.usb_conn import USBConnection
        usb_conn = USBConnection()
        print(f"USB connection created: VID=0x{usb_conn.vendor_id:04X}, PID=0x{usb_conn.product_id:04X}")
        
        # Test device enumeration
        devices = usb_conn.get_available_devices()
        print(f"Available USB devices: {len(devices)}")
    except ImportError as e:
        print(f"USB connection test skipped: {e}")
    
    # Test TCP connections
    from calltag_controller.connections.tcp_client import TCPClientConnection
    from calltag_controller.connections.tcp_server import TCPServerConnection
    
    tcp_client = TCPClientConnection('127.0.0.1', 60000)
    print(f"TCP client created: {tcp_client.host}:{tcp_client.port}")
    
    tcp_server = TCPServerConnection('127.0.0.1', 60001)
    print(f"TCP server created: {tcp_server.host}:{tcp_server.port}")
    
    print()


def main():
    """Run all tests"""
    print("CallTag Controller Test Suite")
    print("=" * 40)
    
    try:
        test_protocol()
        test_tag_info()
        test_helpers()
        test_json_parsing()
        test_connections()
        
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
