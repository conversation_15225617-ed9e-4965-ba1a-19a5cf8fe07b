# STTech.BytesIO


[![License: Apache 2.0](https://img.shields.io/badge/License-Apache--2.0-brightgreen.svg "Apache")](https://www.apache.org/licenses/LICENSE-2.0)
![Size](https://img.shields.io/github/repo-size/landriesnidis/STTech.BytesIO.svg)
[![Join the chat at https://gitter.im/STTech.BytesIO/community](https://badges.gitter.im/STTech.BytesIO/community.svg)](https://gitter.im/STTech.BytesIO/community?utm_source=badge&utm_medium=badge&utm_campaign=pr-badge&utm_content=badge)
[![MyGet](https://img.shields.io/myget/STTech.BytesIO/v/STTech.BytesIO?color=orange&label=MyGet-Preview)](https://www.myget.org/feed/STTech.BytesIO/package/nuget/STTech.BytesIO)

BytesIO is an easy-to-use .NET library for byte array based communication.
It contains TCP and serial port clients, provides full events and many common extension methods.

---
## References

This library is used in the following projects:

- *Demo.BytesIO* https://github.com/landriesnidis/Demo.BytesIO
