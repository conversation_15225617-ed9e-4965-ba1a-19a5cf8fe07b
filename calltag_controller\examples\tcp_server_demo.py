#!/usr/bin/env python3
"""
TCP Server demo for CallTag Controller
TCP服务器演示
"""

import time
import sys
import os
import json
import threading

# Add parent directory to path to import calltag_controller
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from calltag_controller import TCPServerConnection
from calltag_controller.core.protocol import Protocol
from calltag_controller.core.tag_info import TagInfo
from calltag_controller.utils.helpers import create_json_response


class CallTagServer:
    """
    CallTag TCP Server implementation
    声光标签TCP服务器实现
    """
    
    def __init__(self, host='127.0.0.1', port=60001):
        self.server = TCPServerConnection(host, port)
        self.device_sn = "FF24062720B6"
        self.tag_cache = {}
        self.is_reading = False
        
        # Set up callbacks
        self.server.on_client_connected = self.on_client_connected
        self.server.on_client_disconnected = self.on_client_disconnected
        self.server.on_data_received = self.on_data_received
        self.server.on_error = self.on_error
        
        # Simulation data
        self.simulation_tags = [
            TagInfo("0000123456", 0x01, 1, self.device_sn),
            TagInfo("0000789012", 0x02, 1, self.device_sn),
            TagInfo("0000345678", 0x01, 1, self.device_sn),
        ]
        
        self.simulation_thread = None
        self.stop_simulation = threading.Event()
    
    def start(self):
        """Start the server"""
        print(f"Starting CallTag TCP Server...")
        if self.server.connect():
            print(f"Server started successfully on {self.server.host}:{self.server.port}")
            return True
        else:
            print("Failed to start server")
            return False
    
    def stop(self):
        """Stop the server"""
        print("Stopping server...")
        self.stop_reading()
        self.server.disconnect()
        print("Server stopped")
    
    def on_client_connected(self, address, port):
        """Called when a client connects"""
        print(f"Client connected: {address}:{port}")
    
    def on_client_disconnected(self, address, port):
        """Called when a client disconnects"""
        print(f"Client disconnected: {address}:{port}")
    
    def on_data_received(self, data, address, port):
        """Called when data is received from a client"""
        print(f"Received from {address}:{port}: {data.hex()}")
        
        # Parse the command
        success, parsed = Protocol.parse_response(data)
        if success and parsed:
            self.handle_command(parsed, address, port)
        else:
            print(f"Failed to parse command from {address}:{port}")
    
    def on_error(self, error_msg):
        """Called when an error occurs"""
        print(f"Server error: {error_msg}")
    
    def handle_command(self, parsed_data, address, port):
        """Handle parsed command from client"""
        cmd = parsed_data['command']
        addr = parsed_data['address']
        payload = parsed_data['payload']
        
        print(f"Command: 0x{cmd:02X}, Address: {addr}")
        
        if cmd == Protocol.CMD_START_READ:
            print("Start read command received")
            self.start_reading()
            self.send_ack(addr)
        
        elif cmd == Protocol.CMD_STOP_READ:
            print("Stop read command received")
            self.stop_reading()
            self.send_ack(addr)
        
        elif cmd == Protocol.CMD_INQUIRY:
            print("Inquiry command received")
            self.send_tag_data(addr)
        
        elif cmd == Protocol.CMD_CALL_TAG:
            if len(payload) >= 6:
                tag_id = int.from_bytes(payload[:4], 'big')
                led_state = payload[4]
                time_state = payload[5]
                print(f"Call tag command: ID={tag_id}, LED=0x{led_state:02X}, Time=0x{time_state:02X}")
                self.call_tag(tag_id, led_state, time_state)
                self.send_ack(addr)
        
        elif cmd == Protocol.CMD_STOP_CALL_TAG:
            if len(payload) >= 4:
                tag_id = int.from_bytes(payload[:4], 'big')
                print(f"Stop call tag command: ID={tag_id}")
                self.stop_call_tag(tag_id)
                self.send_ack(addr)
        
        else:
            print(f"Unknown command: 0x{cmd:02X}")
    
    def send_ack(self, addr):
        """Send acknowledgment response"""
        # Simple ACK response
        ack_data = Protocol.create_command_packet(addr, 0x80)  # ACK command
        self.server.send(ack_data)
    
    def send_tag_data(self, addr):
        """Send tag data as JSON response"""
        if self.tag_cache:
            json_response = create_json_response(self.device_sn, list(self.tag_cache.values()))
            response_bytes = json_response.encode('utf-8')
            
            # Send as protocol packet
            response_packet = Protocol.create_command_packet(addr, Protocol.CMD_INQUIRY, response_bytes)
            self.server.send(response_packet)
            print(f"Sent tag data: {json_response}")
        else:
            # Send empty response
            empty_response = create_json_response(self.device_sn, [])
            response_bytes = empty_response.encode('utf-8')
            response_packet = Protocol.create_command_packet(addr, Protocol.CMD_INQUIRY, response_bytes)
            self.server.send(response_packet)
    
    def start_reading(self):
        """Start reading simulation"""
        if not self.is_reading:
            self.is_reading = True
            self.stop_simulation.clear()
            self.simulation_thread = threading.Thread(target=self.simulation_loop, daemon=True)
            self.simulation_thread.start()
            print("Started reading simulation")
    
    def stop_reading(self):
        """Stop reading simulation"""
        if self.is_reading:
            self.is_reading = False
            self.stop_simulation.set()
            if self.simulation_thread:
                self.simulation_thread.join(timeout=2.0)
            print("Stopped reading simulation")
    
    def simulation_loop(self):
        """Simulate tag detection"""
        import random
        
        while not self.stop_simulation.is_set():
            # Randomly add/update tags
            if random.random() < 0.3:  # 30% chance to detect a tag
                tag = random.choice(self.simulation_tags)
                tag_copy = TagInfo(tag.id, tag.state, tag.num, tag.type)
                
                if tag.id in self.tag_cache:
                    self.tag_cache[tag.id].increment_count()
                else:
                    self.tag_cache[tag.id] = tag_copy
                
                print(f"Simulated tag detection: {tag_copy}")
            
            time.sleep(2)  # Check every 2 seconds
    
    def call_tag(self, tag_id, led_state, time_state):
        """Handle call tag command"""
        print(f"Calling tag {tag_id} with LED state 0x{led_state:02X} for time 0x{time_state:02X}")
        # In a real implementation, this would send the command to the hardware
        
        # Update tag state if it exists
        tag_id_str = f"{tag_id:010d}"
        if tag_id_str in self.tag_cache:
            self.tag_cache[tag_id_str].update_state(0xFF)  # Mark as being called
    
    def stop_call_tag(self, tag_id):
        """Handle stop call tag command"""
        print(f"Stopping call for tag {tag_id}")
        # In a real implementation, this would send the stop command to the hardware
        
        # Update tag state if it exists
        tag_id_str = f"{tag_id:010d}"
        if tag_id_str in self.tag_cache:
            self.tag_cache[tag_id_str].update_state(0x00)  # Mark as normal


def main():
    """Main function"""
    print("CallTag TCP Server Demo")
    print("This server simulates a CallTag device and responds to client commands")
    
    server = CallTagServer()
    
    try:
        if server.start():
            print("\nServer is running. Commands:")
            print("  'start' - Start reading simulation")
            print("  'stop'  - Stop reading simulation")
            print("  'tags'  - Show cached tags")
            print("  'clear' - Clear tag cache")
            print("  'quit'  - Exit server")
            print()
            
            while True:
                try:
                    cmd = input("Server> ").strip().lower()
                    
                    if cmd == 'quit':
                        break
                    elif cmd == 'start':
                        server.start_reading()
                    elif cmd == 'stop':
                        server.stop_reading()
                    elif cmd == 'tags':
                        tags = list(server.tag_cache.values())
                        print(f"Cached tags ({len(tags)}):")
                        for tag in tags:
                            print(f"  {tag}")
                    elif cmd == 'clear':
                        server.tag_cache.clear()
                        print("Tag cache cleared")
                    elif cmd == 'help':
                        print("Available commands: start, stop, tags, clear, quit")
                    elif cmd:
                        print(f"Unknown command: {cmd}")
                
                except EOFError:
                    break
        
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        server.stop()


if __name__ == '__main__':
    main()
