"""
Communication protocol implementation for CallTag devices
声光标签设备通信协议实现
"""

import struct
from typing import List, Tuple, Optional
import json
from .tag_info import TagInfo


class Protocol:
    """
    Implements the communication protocol for CallTag devices
    实现声光标签设备的通信协议
    """
    
    # Command constants (指令常量)
    CMD_START_READ = 0x41      # 开始读指令
    CMD_STOP_READ = 0x40       # 停止读指令
    CMD_INQUIRY = 0x43         # 询盘指令
    CMD_CALL_TAG = 0x65        # 呼叫指令
    CMD_STOP_CALL_TAG = 0x66   # 停止呼叫指令
    CMD_GET_SYSTEM_INFO = 0x20 # 获取系统信息
    CMD_GET_DEVICE_PARAM = 0x21 # 获取设备参数
    CMD_SET_DEVICE_PARAM = 0x22 # 设置设备参数
    CMD_GET_CACHE = 0x43       # 获取缓存数据
    
    # LED modes (LED模式)
    LED_RED_ONLY = 0x00        # 单独红灯
    LED_GREEN_ONLY = 0x01      # 单独绿灯
    LED_CROSS_MODE = 0x02      # 交叉模式
    LED_ALTERNATE_MODE = 0x03  # 交替模式
    LED_FLASH_MODE = 0x04      # 爆闪模式
    
    # Time durations (时间选项)
    TIME_10_SEC = 0x00         # 10秒
    TIME_30_SEC = 0x01         # 30秒
    TIME_60_SEC = 0x02         # 60秒
    TIME_5_MIN = 0x03          # 5分钟
    TIME_PERMANENT = 0x04      # 永久
    
    @staticmethod
    def calculate_checksum(data: bytes) -> int:
        """
        Calculate checksum for the data
        计算数据校验和
        
        Args:
            data (bytes): Data to calculate checksum for
            
        Returns:
            int: Checksum value
        """
        checksum = sum(data) & 0xFF
        return ((~checksum) + 1) & 0xFF
    
    @staticmethod
    def create_command_packet(addr: int, cmd: int, params: bytes = b'') -> bytes:
        """
        Create a command packet
        创建指令数据包
        
        Args:
            addr (int): Device address (设备地址)
            cmd (int): Command code (指令码)
            params (bytes): Command parameters (指令参数)
            
        Returns:
            bytes: Complete command packet
        """
        # Packet format: 'S' 'W' [LEN_H] [LEN_L] [ADDR] [CMD] [PARAMS...] [CHECKSUM]
        param_len = len(params)
        total_len = param_len + 3  # addr + cmd + checksum
        
        packet = bytearray()
        packet.extend(b'SW')  # Header
        packet.append(0)      # Length high byte
        packet.append(total_len)  # Length low byte
        packet.append(addr)   # Device address
        packet.append(cmd)    # Command
        packet.extend(params) # Parameters
        
        # Calculate and append checksum
        checksum = Protocol.calculate_checksum(packet)
        packet.append(checksum)
        
        return bytes(packet)
    
    @staticmethod
    def create_start_read_command(addr: int = 1) -> bytes:
        """Create start read command (创建开始读指令)"""
        return Protocol.create_command_packet(addr, Protocol.CMD_START_READ)
    
    @staticmethod
    def create_stop_read_command(addr: int = 1) -> bytes:
        """Create stop read command (创建停止读指令)"""
        return Protocol.create_command_packet(addr, Protocol.CMD_STOP_READ)
    
    @staticmethod
    def create_inquiry_command(addr: int = 1) -> bytes:
        """Create inquiry command (创建询盘指令)"""
        return Protocol.create_command_packet(addr, Protocol.CMD_INQUIRY)
    
    @staticmethod
    def create_call_tag_command(addr: int, tag_id: int, led_mode: int, time_mode: int, buzzer_enable: bool = True) -> bytes:
        """
        Create call tag command (创建呼叫标签指令)
        
        Args:
            addr (int): Device address
            tag_id (int): Tag ID to call
            led_mode (int): LED mode (0-4)
            time_mode (int): Time duration mode (0-4)
            buzzer_enable (bool): Enable buzzer
            
        Returns:
            bytes: Call tag command packet
        """
        # Convert tag_id to 4 bytes (big endian)
        id_bytes = struct.pack('>I', tag_id)
        
        # Combine LED mode and buzzer enable
        led_state = led_mode
        if buzzer_enable:
            led_state |= 0x10
        
        params = id_bytes + bytes([led_state, time_mode])
        return Protocol.create_command_packet(addr, Protocol.CMD_CALL_TAG, params)
    
    @staticmethod
    def create_stop_call_command(addr: int, tag_id: int) -> bytes:
        """
        Create stop call command (创建停止呼叫指令)
        
        Args:
            addr (int): Device address
            tag_id (int): Tag ID to stop calling
            
        Returns:
            bytes: Stop call command packet
        """
        id_bytes = struct.pack('>I', tag_id)
        params = id_bytes + b'\x00\x00'  # LED state and time set to 0
        return Protocol.create_command_packet(addr, Protocol.CMD_STOP_CALL_TAG, params)
    
    @staticmethod
    def parse_response(data: bytes) -> Tuple[bool, Optional[dict]]:
        """
        Parse response data from device
        解析设备响应数据
        
        Args:
            data (bytes): Raw response data
            
        Returns:
            Tuple[bool, Optional[dict]]: (success, parsed_data)
        """
        if len(data) < 7:  # Minimum packet size
            return False, None
        
        # Check header
        if data[:2] != b'SW':
            return False, None
        
        # Extract length
        length = (data[2] << 8) | data[3]
        
        if len(data) < length + 4:  # Header + length + data
            return False, None
        
        # Verify checksum
        packet_data = data[:length + 4 - 1]  # Exclude checksum
        expected_checksum = Protocol.calculate_checksum(packet_data)
        actual_checksum = data[length + 4 - 1]
        
        if expected_checksum != actual_checksum:
            return False, None
        
        # Extract command and data
        addr = data[4]
        cmd = data[5]
        payload = data[6:length + 4 - 1]
        
        return True, {
            'address': addr,
            'command': cmd,
            'payload': payload
        }
    
    @staticmethod
    def parse_tag_data(json_data: str) -> List[TagInfo]:
        """
        Parse JSON tag data from device response
        解析设备响应中的JSON标签数据
        
        Args:
            json_data (str): JSON string containing tag data
            
        Returns:
            List[TagInfo]: List of parsed tag information
        """
        try:
            data = json.loads(json_data)
            tags = []
            
            if 'AttenceID' in data:
                dev_sn = data.get('DevSN', '')
                for item in data['AttenceID']:
                    tag_id = item.get('ID', '')
                    # Convert hex ID to decimal if needed
                    if tag_id:
                        try:
                            decimal_id = int(tag_id, 16)
                            tag_id = f"{decimal_id:010d}"
                        except ValueError:
                            pass  # Keep original if conversion fails
                    
                    tag_info = TagInfo(
                        tag_id=tag_id,
                        state=int(item.get('State', 0)),
                        num=1,  # Initial count
                        tag_type=dev_sn
                    )
                    tags.append(tag_info)
            
            return tags
        except (json.JSONDecodeError, KeyError, ValueError):
            return []
