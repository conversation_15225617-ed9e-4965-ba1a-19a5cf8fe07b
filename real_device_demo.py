#!/usr/bin/env python3
"""
Real device demo for CallTag Controller
真实设备演示

This script demonstrates how to connect to and control real CallTag devices.
"""

import time
import sys
from calltag_controller import CallTagDevice, SerialConnection, TCPClientConnection
from calltag_controller.core.protocol import Protocol


def demo_serial_connection():
    """Demo with serial connection"""
    print("=== Serial Connection Demo ===")
    
    # List available ports
    temp_conn = SerialConnection('COM1', 115200)  # Temporary connection for port listing
    available_ports = temp_conn.available_ports()
    
    print(f"Available serial ports: {available_ports}")
    
    if not available_ports:
        print("No serial ports found!")
        return False
    
    # Let user choose port
    print("Available ports:")
    for i, port in enumerate(available_ports):
        print(f"  {i}: {port}")
    
    try:
        choice = int(input(f"Choose port (0-{len(available_ports)-1}): "))
        if 0 <= choice < len(available_ports):
            selected_port = available_ports[choice]
        else:
            print("Invalid choice, using first port")
            selected_port = available_ports[0]
    except ValueError:
        print("Invalid input, using first port")
        selected_port = available_ports[0]
    
    print(f"Using port: {selected_port}")
    
    # Create connection and device
    connection = SerialConnection(selected_port, 115200)
    device = CallTagDevice(connection)
    
    # Set up callbacks
    def on_tag_detected(tag_info):
        print(f"[SERIAL] Tag detected: {tag_info}")
    
    def on_error(error_msg):
        print(f"[SERIAL] Error: {error_msg}")
    
    device.on_tag_detected = on_tag_detected
    device.on_error = on_error
    
    try:
        print("Connecting to device...")
        if device.connect():
            print("Connected successfully!")
            
            # Get device info
            info = device.get_device_info()
            print(f"Device info: {info}")
            
            # Start reading
            print("Starting to read tags...")
            if device.start_reading():
                print("Reading started. Waiting for tags...")
                
                # Read for 10 seconds
                time.sleep(10)
                
                # Show detected tags
                tags = device.get_cached_tags()
                print(f"\nDetected {len(tags)} unique tags:")
                for tag in tags:
                    print(f"  {tag}")
                
                # If we have tags, try calling one
                if tags:
                    tag_to_call = tags[0]
                    tag_id = int(tag_to_call.id)
                    
                    print(f"\nCalling tag {tag_id}...")
                    device.call_tag(tag_id, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True)
                    
                    print("Tag called! You should see/hear the alert.")
                    print("Waiting 10 seconds...")
                    time.sleep(10)
                    
                    print("Stopping call...")
                    device.stop_call_tag(tag_id)
                    print("Call stopped.")
                
                device.stop_reading()
                print("Stopped reading")
            else:
                print("Failed to start reading")
                return False
        else:
            print("Failed to connect to device")
            return False
    
    finally:
        device.disconnect()
        print("Disconnected from device")
    
    return True


def demo_tcp_connection():
    """Demo with TCP connection"""
    print("\n=== TCP Connection Demo ===")
    
    # Get connection parameters
    host = input("Enter device IP address (default: *************): ").strip()
    if not host:
        host = "*************"
    
    port_str = input("Enter device port (default: 60000): ").strip()
    if not port_str:
        port = 60000
    else:
        try:
            port = int(port_str)
        except ValueError:
            print("Invalid port, using default 60000")
            port = 60000
    
    print(f"Connecting to {host}:{port}")
    
    # Create connection and device
    connection = TCPClientConnection(host, port)
    device = CallTagDevice(connection)
    
    # Set up callbacks
    def on_tag_detected(tag_info):
        print(f"[TCP] Tag detected: {tag_info}")
    
    def on_error(error_msg):
        print(f"[TCP] Error: {error_msg}")
    
    def on_connection_lost():
        print("[TCP] Connection lost!")
    
    device.on_tag_detected = on_tag_detected
    device.on_error = on_error
    device.on_connection_lost = on_connection_lost
    
    try:
        print("Connecting to device...")
        if device.connect():
            print("Connected successfully!")
            
            # Start reading
            print("Starting to read tags...")
            if device.start_reading():
                print("Reading started. Waiting for tags...")
                
                # Read for 15 seconds
                time.sleep(15)
                
                # Show detected tags
                tags = device.get_cached_tags()
                print(f"\nDetected {len(tags)} unique tags:")
                for tag in tags:
                    print(f"  {tag}")
                
                # Interactive mode
                print("\nEntering interactive mode...")
                print("Commands: call <id>, stop <id>, tags, quit")
                
                while True:
                    try:
                        cmd = input("TCP> ").strip().split()
                        if not cmd:
                            continue
                        
                        if cmd[0].lower() == 'quit':
                            break
                        elif cmd[0].lower() == 'call' and len(cmd) > 1:
                            try:
                                tag_id = int(cmd[1])
                                device.call_tag(tag_id, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True)
                                print(f"Called tag {tag_id}")
                            except ValueError:
                                print("Invalid tag ID")
                        elif cmd[0].lower() == 'stop' and len(cmd) > 1:
                            try:
                                tag_id = int(cmd[1])
                                device.stop_call_tag(tag_id)
                                print(f"Stopped calling tag {tag_id}")
                            except ValueError:
                                print("Invalid tag ID")
                        elif cmd[0].lower() == 'tags':
                            current_tags = device.get_cached_tags()
                            print(f"Current tags ({len(current_tags)}):")
                            for tag in current_tags:
                                print(f"  {tag}")
                        else:
                            print("Unknown command")
                    except EOFError:
                        break
                
                device.stop_reading()
                print("Stopped reading")
            else:
                print("Failed to start reading")
                return False
        else:
            print("Failed to connect to device")
            return False
    
    finally:
        device.disconnect()
        print("Disconnected from device")
    
    return True


def main():
    """Main function"""
    print("CallTag Controller - Real Device Demo")
    print("=" * 40)
    print()
    print("This demo will help you connect to and test your CallTag device.")
    print("Make sure your device is connected and powered on.")
    print()
    
    print("Choose connection type:")
    print("1. Serial (COM port)")
    print("2. TCP Client (network)")
    print("3. Exit")
    
    try:
        choice = input("Enter choice (1-3): ").strip()
        
        if choice == '1':
            return demo_serial_connection()
        elif choice == '2':
            return demo_tcp_connection()
        elif choice == '3':
            print("Exiting...")
            return True
        else:
            print("Invalid choice")
            return False
    
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
        return True
    except Exception as e:
        print(f"Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    if success:
        print("\nDemo completed successfully!")
        print("\nNext steps:")
        print("1. Integrate the library into your application")
        print("2. Customize the callbacks for your needs")
        print("3. Add error handling and logging")
        print("4. Test with your specific tag IDs")
    else:
        print("\nDemo failed. Please check:")
        print("1. Device is connected and powered on")
        print("2. Correct port/IP address and settings")
        print("3. Device drivers are installed")
        print("4. No other software is using the device")
    
    sys.exit(0 if success else 1)
