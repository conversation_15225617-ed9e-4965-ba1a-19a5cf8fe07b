"""
TCP Server connection implementation for CallTag devices
TCP服务器连接实现
"""

import socket
import threading
import time
from typing import Optional, List, Callable, Dict, Any


class TCPServerConnection:
    """
    TCP Server connection for CallTag devices
    声光标签设备的TCP服务器连接
    """
    
    def __init__(self, host: str = '127.0.0.1', port: int = 60001):
        """
        Initialize TCP server connection
        
        Args:
            host (str): Server bind address
            port (int): Server port number
        """
        self.host = host
        self.port = port
        self.server_socket: Optional[socket.socket] = None
        self.client_sockets: List[socket.socket] = []
        self.client_info: Dict[socket.socket, dict] = {}
        self.is_running = False
        self.accept_thread: Optional[threading.Thread] = None
        self.client_threads: List[threading.Thread] = []
        
        # Callbacks
        self.on_client_connected: Optional[Callable[[str, int], None]] = None
        self.on_client_disconnected: Optional[Callable[[str, int], None]] = None
        self.on_data_received: Optional[Callable[[bytes, str, int], None]] = None
        self.on_error: Optional[Callable[[str], None]] = None
    
    def connect(self) -> bool:
        """
        Start the TCP server
        启动TCP服务器
        
        Returns:
            bool: True if server started successfully
        """
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            
            # Bind and listen
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.is_running = True
            
            # Start accept thread
            self.accept_thread = threading.Thread(target=self._accept_loop, daemon=True)
            self.accept_thread.start()
            
            print(f"TCP Server listening on {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"Failed to start TCP server: {e}")
            if self.on_error:
                self.on_error(f"Failed to start TCP server: {e}")
            return False
    
    def disconnect(self):
        """
        Stop the TCP server
        停止TCP服务器
        """
        self.is_running = False
        
        # Close all client connections
        for client_socket in self.client_sockets.copy():
            self._disconnect_client(client_socket)
        
        # Close server socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
            self.server_socket = None
        
        # Wait for threads to finish
        if self.accept_thread and self.accept_thread.is_alive():
            self.accept_thread.join(timeout=2.0)
        
        for thread in self.client_threads:
            if thread.is_alive():
                thread.join(timeout=1.0)
        
        self.client_threads.clear()
        print("TCP Server stopped")
    
    def send(self, data: bytes, client_socket: Optional[socket.socket] = None) -> bool:
        """
        Send data to client(s)
        向客户端发送数据
        
        Args:
            data (bytes): Data to send
            client_socket (Optional[socket.socket]): Specific client to send to, or None for all clients
            
        Returns:
            bool: True if sent successfully to at least one client
        """
        if not self.is_running:
            return False
        
        success = False
        
        if client_socket:
            # Send to specific client
            if client_socket in self.client_sockets:
                success = self._send_to_client(client_socket, data)
        else:
            # Send to all clients
            for client in self.client_sockets.copy():
                if self._send_to_client(client, data):
                    success = True
        
        return success
    
    def receive(self, max_size: int = 4096) -> bytes:
        """
        This method is not applicable for server mode
        此方法不适用于服务器模式
        
        Use the on_data_received callback instead
        请使用 on_data_received 回调函数
        """
        return b''
    
    def get_connected_clients(self) -> List[dict]:
        """
        Get list of connected clients
        获取已连接客户端列表
        
        Returns:
            List[dict]: List of client information
        """
        clients = []
        for client_socket in self.client_sockets:
            if client_socket in self.client_info:
                clients.append(self.client_info[client_socket].copy())
        return clients
    
    def disconnect_client(self, client_address: str, client_port: int):
        """
        Disconnect a specific client
        断开指定客户端连接
        
        Args:
            client_address (str): Client IP address
            client_port (int): Client port
        """
        for client_socket in self.client_sockets.copy():
            if client_socket in self.client_info:
                info = self.client_info[client_socket]
                if info['address'] == client_address and info['port'] == client_port:
                    self._disconnect_client(client_socket)
                    break
    
    def _accept_loop(self):
        """
        Main accept loop running in separate thread
        在独立线程中运行的主要接受循环
        """
        while self.is_running:
            try:
                if self.server_socket:
                    self.server_socket.settimeout(1.0)  # Allow periodic checking of is_running
                    client_socket, client_address = self.server_socket.accept()
                    
                    # Add client
                    self.client_sockets.append(client_socket)
                    self.client_info[client_socket] = {
                        'address': client_address[0],
                        'port': client_address[1],
                        'connected_time': time.time()
                    }
                    
                    # Start client handler thread
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
                    self.client_threads.append(client_thread)
                    
                    print(f"Client connected: {client_address[0]}:{client_address[1]}")
                    
                    # Notify callback
                    if self.on_client_connected:
                        self.on_client_connected(client_address[0], client_address[1])
                        
            except socket.timeout:
                continue  # Check is_running flag
            except Exception as e:
                if self.is_running:  # Only log if we're supposed to be running
                    print(f"Accept loop error: {e}")
                    if self.on_error:
                        self.on_error(f"Accept loop error: {e}")
                break
    
    def _handle_client(self, client_socket: socket.socket):
        """
        Handle communication with a specific client
        处理与特定客户端的通信
        
        Args:
            client_socket (socket.socket): Client socket
        """
        client_info = self.client_info.get(client_socket, {})
        client_address = client_info.get('address', 'unknown')
        client_port = client_info.get('port', 0)
        
        try:
            while self.is_running and client_socket in self.client_sockets:
                try:
                    client_socket.settimeout(1.0)
                    data = client_socket.recv(4096)
                    
                    if not data:
                        # Client disconnected
                        break
                    
                    # Notify callback
                    if self.on_data_received:
                        self.on_data_received(data, client_address, client_port)
                        
                except socket.timeout:
                    continue  # Check is_running flag
                except Exception as e:
                    print(f"Client handler error: {e}")
                    break
                    
        finally:
            self._disconnect_client(client_socket)
    
    def _send_to_client(self, client_socket: socket.socket, data: bytes) -> bool:
        """
        Send data to a specific client
        向特定客户端发送数据
        
        Args:
            client_socket (socket.socket): Client socket
            data (bytes): Data to send
            
        Returns:
            bool: True if sent successfully
        """
        try:
            bytes_sent = client_socket.send(data)
            return bytes_sent == len(data)
        except Exception as e:
            print(f"Failed to send to client: {e}")
            self._disconnect_client(client_socket)
            return False
    
    def _disconnect_client(self, client_socket: socket.socket):
        """
        Disconnect a client
        断开客户端连接
        
        Args:
            client_socket (socket.socket): Client socket to disconnect
        """
        if client_socket in self.client_sockets:
            client_info = self.client_info.get(client_socket, {})
            client_address = client_info.get('address', 'unknown')
            client_port = client_info.get('port', 0)
            
            try:
                client_socket.close()
            except:
                pass
            
            self.client_sockets.remove(client_socket)
            if client_socket in self.client_info:
                del self.client_info[client_socket]
            
            print(f"Client disconnected: {client_address}:{client_port}")
            
            # Notify callback
            if self.on_client_disconnected:
                self.on_client_disconnected(client_address, client_port)
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
