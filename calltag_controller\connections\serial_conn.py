"""
Serial connection implementation for CallTag devices
串口连接实现
"""

import serial
import time
from typing import Optional


class SerialConnection:
    """
    Serial connection for CallTag devices
    声光标签设备的串口连接
    """
    
    def __init__(self, port: str, baudrate: int = 115200, timeout: float = 1.0):
        """
        Initialize serial connection
        
        Args:
            port (str): Serial port name (e.g., 'COM1', '/dev/ttyUSB0')
            baudrate (int): Baud rate (default: 115200)
            timeout (float): Read timeout in seconds
        """
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_conn: Optional[serial.Serial] = None
        self.is_connected = False
    
    def connect(self) -> bool:
        """
        Connect to the serial port
        连接串口
        
        Returns:
            bool: True if connection successful
        """
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )
            
            if self.serial_conn.is_open:
                self.is_connected = True
                # Clear any existing data
                self.serial_conn.reset_input_buffer()
                self.serial_conn.reset_output_buffer()
                return True
            
            return False
        except Exception as e:
            print(f"Serial connection failed: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the serial port
        断开串口连接
        """
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
        self.is_connected = False
    
    def send(self, data: bytes) -> bool:
        """
        Send data through serial port
        通过串口发送数据
        
        Args:
            data (bytes): Data to send
            
        Returns:
            bool: True if sent successfully
        """
        if not self.is_connected or not self.serial_conn:
            return False
        
        try:
            bytes_written = self.serial_conn.write(data)
            self.serial_conn.flush()
            return bytes_written == len(data)
        except Exception as e:
            print(f"Serial send failed: {e}")
            return False
    
    def receive(self, max_size: int = 4096) -> bytes:
        """
        Receive data from serial port
        从串口接收数据
        
        Args:
            max_size (int): Maximum bytes to read
            
        Returns:
            bytes: Received data
        """
        if not self.is_connected or not self.serial_conn:
            return b''
        
        try:
            if self.serial_conn.in_waiting > 0:
                return self.serial_conn.read(min(max_size, self.serial_conn.in_waiting))
            return b''
        except Exception as e:
            print(f"Serial receive failed: {e}")
            return b''
    
    def available_ports(self) -> list:
        """
        Get list of available serial ports
        获取可用串口列表
        
        Returns:
            list: List of available port names
        """
        try:
            import serial.tools.list_ports
            ports = serial.tools.list_ports.comports()
            return [port.device for port in ports]
        except ImportError:
            print("pyserial not installed. Install with: pip install pyserial")
            return []
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
