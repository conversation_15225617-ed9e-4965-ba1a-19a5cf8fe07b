"""
TagInfo class for representing tag information
标签信息类
"""

from typing import Optional
import json


class TagInfo:
    """
    Represents information about a detected tag
    表示检测到的标签信息
    """
    
    def __init__(self, tag_id: str = "", state: int = 0, num: int = 0, tag_type: str = ""):
        """
        Initialize TagInfo
        
        Args:
            tag_id (str): Tag ID (标签ID)
            state (int): Tag state (标签状态)
            num (int): Detection count (检测次数)
            tag_type (str): Tag type (标签类型)
        """
        self.id = tag_id
        self.state = state
        self.num = num
        self.type = tag_type
    
    def __str__(self) -> str:
        """String representation of TagInfo"""
        return f"TagInfo(ID={self.id}, State={self.state:02X}, Num={self.num}, Type={self.type})"
    
    def __repr__(self) -> str:
        """Detailed representation of TagInfo"""
        return self.__str__()
    
    def to_dict(self) -> dict:
        """Convert TagInfo to dictionary"""
        return {
            'ID': self.id,
            'State': self.state,
            'Num': self.num,
            'Type': self.type
        }
    
    def to_json(self) -> str:
        """Convert TagInfo to JSON string"""
        return json.dumps(self.to_dict())
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TagInfo':
        """Create TagInfo from dictionary"""
        return cls(
            tag_id=data.get('ID', ''),
            state=data.get('State', 0),
            num=data.get('Num', 0),
            tag_type=data.get('Type', '')
        )
    
    @classmethod
    def from_json(cls, json_str: str) -> 'TagInfo':
        """Create TagInfo from JSON string"""
        data = json.loads(json_str)
        return cls.from_dict(data)
    
    def increment_count(self):
        """Increment the detection count"""
        self.num += 1
    
    def update_state(self, new_state: int):
        """Update the tag state"""
        self.state = new_state
