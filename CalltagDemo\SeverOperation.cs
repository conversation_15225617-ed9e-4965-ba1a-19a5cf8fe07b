﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.Net;
using System.Net.Sockets;
using System.Windows.Forms;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;

namespace CalltagDemo
{
    class SeverOperation
    {
        //Sever端询盘及呼叫指令
        //开始读指令
        public static byte StarReadCmd = 0x41;
        //停止读指令
        public static byte StopReadCmd = 0x40;
        // 询盘指令
        public static  byte InquiryCmd = 0x43;
        // 停止询盘指令

        // 呼叫指令
        public static byte CallTagCmd = 0x65;
        //停止呼叫指令
        public static byte StopCallTagCmd = 0x66;
        //设备地址
        public static  byte sAddr = 1;
        //呼叫指令
        //停止呼叫指令
        //创建一个和客户端通信的套接字
        Dictionary<string, Socket> dic = new Dictionary<string, Socket> { };   //定义一个集合，存储客户端信息

        // sever端，询盘操作
        public static  byte GetCheckSum(byte[] pucBuff, int iBuffLen)  //内部函数
        {
             byte ucTmp = 0x00;
             int iIndex = 0;
	         for(iIndex =0 ; iIndex<iBuffLen ;iIndex++)
	         {
		       ucTmp += pucBuff[iIndex];
	         }
             ucTmp = (byte)((~ucTmp)+1);
	         return ucTmp;
          }


        //  addr: 设备地址 1~255; cmd:指令;  param:参数 ；iLength：参数数据长度, buf:输出数据；
        public static int SWNet_ReadySendData(byte addr,byte cmd, byte[] param, byte iLength, byte[] buf )  
        {
            int len = 0;
            //byte[] buf = new byte[256];
            buf[0] = (byte)'S';
            buf[1] = (byte)'W';

            //buf[2] = (byte)((iLength + 3) >> 8);
            buf[2] = 0;
            buf[3] = (byte)(iLength + 3);

            buf[4] = addr;
            buf[5] = cmd;

            if(iLength>0)
            {
                for (int i = 0; i < iLength; i++)
                {
                    buf[6 + i] = param[i];
                }
            }
            buf[6 + iLength] = GetCheckSum(buf, 6 + iLength);
            len = 7 + iLength;

            return len;
        }


        //开始读指令
        public static void Star_Read()
        {
            int length = 0;
            byte[] idata = new byte[256];
            byte[] udata = new byte[256];
            length = SWNet_ReadySendData(sAddr, StarReadCmd, idata, 0, udata);
            if (length > 0)
            {
                Form1.NetReciveFlag = false;
                Form1.JytSendOut(udata, length);   // 发送;
            }
        }

        // 停止读指令
        public static void Stop_Read()
        {
            int length = 0;
            byte[] idata = new byte[256];
            byte[] udata = new byte[256];
            length = SWNet_ReadySendData(sAddr, StopReadCmd, idata, 0, udata);
            if (udata.Length > 0)
            {
                Form1.NetReciveFlag = false;
                Form1.JytSendOut(udata, length);   // 发送;
            }
        }

        // 询盘指令;

        public static void Sever_InquiryOperation(byte addr)
        {
            int length = 0;
            byte[] idata = new byte[256];
            byte[] uData = new byte[1024 * 2];

            length = SWNet_ReadySendData(addr, InquiryCmd, idata, 0, uData);

            if (uData.Length > 0)
            {
                Form1.NetReciveFlag = false;
                Form1.JytSendOut(uData, length);   // 发送;
            }
        }

        //呼叫指令
        public static void  Sever_CallOperation(byte addr, byte[] id, byte data1 ,byte data2)
        {
            int length = 0;
            byte[] idata = new byte[16];
            byte[] uData = new byte[1024*2];

            idata[0] = id[0]; idata[1] = id[1]; idata[2] = id[2]; idata[3] = id[3];
            idata[4] = data1; idata[5] = data2;

            length = SWNet_ReadySendData(addr, CallTagCmd, idata, 6, uData);

            if (uData.Length > 0)
            {
                Form1.NetReciveFlag = false;
                Form1.JytSendOut(uData, length);   // 发送;
            }
        }


        // 停止呼叫指令;
        public static void Sever_StopInquiryOperation(byte addr, byte[] id, byte data1, byte data2)
        {
            int length = 0;
            byte[] idata = new byte[16];
            byte[] uData = new byte[1024 * 2];

            //Array.Copy(id, idata, 4);
            idata[0] = id[0]; idata[1] = id[1]; idata[2] = id[2]; idata[3] = id[3];
            idata[4] = 0; idata[5] = 0;

            length = SWNet_ReadySendData(addr, StopCallTagCmd, idata, 6, uData);

            if (uData.Length > 0)
            {
                Form1.NetReciveFlag = false;
                Form1.JytSendOut(uData, length);   // 发送;
            }
        }

        // 停止呼叫指令;


        private void BeginInvoke(MethodInvoker methodInvoker)
        {
            throw new NotImplementedException();
        }

        //internal static void Sever_InquiryOperation(byte starReadCmd, byte v, object addr)
        //{
        //    //throw new NotImplementedException(); 
        //}

        //internal void Sever_InquiryOperation(byte starReadCmd, int v)
        //{
        //    //throw new NotImplementedException();
        //}
    } 
}
