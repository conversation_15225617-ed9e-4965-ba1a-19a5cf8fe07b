#!/usr/bin/env python3
"""
Test script for real CallTag device
真实设备测试脚本

Device IP: *************
Device Port: 60000
"""

import time
import sys
from calltag_controller import CallTagDevice, TCPClientConnection
from calltag_controller.core.protocol import Protocol


class RealDeviceTest:
    """Real device test class"""
    
    def __init__(self):
        self.device_ip = "*************"
        self.device_port = 60000
        self.device = None
        self.detected_tags = []
        
    def setup_device(self):
        """Setup device connection"""
        print(f"Setting up connection to {self.device_ip}:{self.device_port}")
        
        # Create TCP connection
        connection = TCPClientConnection(self.device_ip, self.device_port, timeout=10.0)
        self.device = CallTagDevice(connection)
        
        # Set up callbacks
        self.device.on_tag_detected = self.on_tag_detected
        self.device.on_error = self.on_error
        self.device.on_connection_lost = self.on_connection_lost
        
        print("Device setup complete")
    
    def on_tag_detected(self, tag_info):
        """Callback when tag is detected"""
        print(f"🏷️  Tag detected: ID={tag_info.id}, State=0x{tag_info.state:02X}, Count={tag_info.num}")
        
        # Update our list
        existing = next((t for t in self.detected_tags if t.id == tag_info.id), None)
        if existing:
            existing.num = tag_info.num
            existing.state = tag_info.state
        else:
            self.detected_tags.append(tag_info)
    
    def on_error(self, error_msg):
        """Callback for errors"""
        print(f"❌ Error: {error_msg}")
    
    def on_connection_lost(self):
        """Callback when connection is lost"""
        print("⚠️  Connection lost!")
    
    def connect(self):
        """Connect to device"""
        print("Connecting to device...")
        if self.device.connect():
            print("✅ Connected successfully!")
            
            # Get device info
            info = self.device.get_device_info()
            print(f"📱 Device info: {info}")
            return True
        else:
            print("❌ Failed to connect!")
            return False
    
    def test_basic_communication(self):
        """Test basic communication"""
        print("\n=== Testing Basic Communication ===")
        
        if not self.device or not self.device.is_connected:
            print("Device not connected!")
            return False
        
        print("📡 Starting tag reading...")
        if self.device.start_reading():
            print("✅ Reading started successfully")
            
            # Read for 10 seconds
            print("⏱️  Reading tags for 10 seconds...")
            for i in range(10):
                print(f"  {10-i} seconds remaining...", end='\r')
                time.sleep(1)
            print("\n")
            
            # Show results
            print(f"📊 Found {len(self.detected_tags)} unique tags:")
            for tag in self.detected_tags:
                print(f"   • ID: {tag.id}, State: 0x{tag.state:02X}, Detections: {tag.num}")
            
            self.device.stop_reading()
            print("⏹️  Stopped reading")
            return True
        else:
            print("❌ Failed to start reading")
            return False
    
    def test_tag_calling(self):
        """Test calling tags"""
        print("\n=== Testing Tag Calling ===")
        
        if not self.detected_tags:
            print("No tags detected yet. Please run basic communication test first.")
            return False
        
        # Use the first detected tag
        test_tag = self.detected_tags[0]
        tag_id = int(test_tag.id)
        
        print(f"🔊 Testing with tag ID: {tag_id}")
        
        # Test different LED modes and durations
        test_cases = [
            ("Flash Mode, 10 seconds", Protocol.LED_FLASH_MODE, Protocol.TIME_10_SEC),
            ("Alternate Mode, 30 seconds", Protocol.LED_ALTERNATE_MODE, Protocol.TIME_30_SEC),
        ]
        
        for description, led_mode, time_mode in test_cases:
            print(f"\n🔄 Testing: {description}")
            
            # Call tag
            if self.device.call_tag(tag_id, led_mode, time_mode, True):
                print(f"✅ Call command sent successfully")
                print("   You should see the tag flashing and hear the buzzer")
                
                # Wait a bit
                wait_time = 5  # Wait 5 seconds to observe
                print(f"⏱️  Waiting {wait_time} seconds to observe...")
                time.sleep(wait_time)
                
                # Stop calling
                if self.device.stop_call_tag(tag_id):
                    print("⏹️  Stop call command sent successfully")
                else:
                    print("❌ Failed to send stop call command")
            else:
                print("❌ Failed to send call command")
            
            time.sleep(2)  # Brief pause between tests
        
        return True
    
    def interactive_mode(self):
        """Interactive mode for manual testing"""
        print("\n=== Interactive Mode ===")
        print("Available commands:")
        print("  start     - Start reading tags")
        print("  stop      - Stop reading tags")
        print("  tags      - Show detected tags")
        print("  call <id> - Call tag with specified ID")
        print("  stopcall <id> - Stop calling tag")
        print("  clear     - Clear detected tags")
        print("  status    - Show device status")
        print("  help      - Show this help")
        print("  quit      - Exit interactive mode")
        print()
        
        while True:
            try:
                cmd = input("Device> ").strip().split()
                
                if not cmd:
                    continue
                
                command = cmd[0].lower()
                
                if command == 'quit':
                    break
                elif command == 'start':
                    if self.device.start_reading():
                        print("✅ Started reading tags")
                    else:
                        print("❌ Failed to start reading")
                
                elif command == 'stop':
                    self.device.stop_reading()
                    print("⏹️  Stopped reading tags")
                
                elif command == 'tags':
                    print(f"📊 Detected tags ({len(self.detected_tags)}):")
                    if self.detected_tags:
                        for i, tag in enumerate(self.detected_tags):
                            print(f"  {i+1}. ID: {tag.id}, State: 0x{tag.state:02X}, Count: {tag.num}")
                    else:
                        print("  No tags detected")
                
                elif command == 'call':
                    if len(cmd) > 1:
                        try:
                            tag_id = int(cmd[1])
                            if self.device.call_tag(tag_id, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True):
                                print(f"🔊 Called tag {tag_id}")
                            else:
                                print(f"❌ Failed to call tag {tag_id}")
                        except ValueError:
                            print("❌ Invalid tag ID. Please enter a number.")
                    else:
                        print("Usage: call <tag_id>")
                
                elif command == 'stopcall':
                    if len(cmd) > 1:
                        try:
                            tag_id = int(cmd[1])
                            if self.device.stop_call_tag(tag_id):
                                print(f"⏹️  Stopped calling tag {tag_id}")
                            else:
                                print(f"❌ Failed to stop calling tag {tag_id}")
                        except ValueError:
                            print("❌ Invalid tag ID. Please enter a number.")
                    else:
                        print("Usage: stopcall <tag_id>")
                
                elif command == 'clear':
                    self.detected_tags.clear()
                    self.device.clear_cache()
                    print("🗑️  Cleared tag cache")
                
                elif command == 'status':
                    print(f"📱 Device connected: {self.device.is_connected}")
                    print(f"📡 Reading active: {self.device.is_reading}")
                    print(f"🏷️  Tags detected: {len(self.detected_tags)}")
                
                elif command == 'help':
                    print("Available commands: start, stop, tags, call, stopcall, clear, status, help, quit")
                
                else:
                    print(f"❌ Unknown command: {command}. Type 'help' for available commands.")
            
            except EOFError:
                break
            except KeyboardInterrupt:
                print("\nUse 'quit' to exit")
    
    def disconnect(self):
        """Disconnect from device"""
        if self.device:
            self.device.stop_reading()
            self.device.disconnect()
            print("🔌 Disconnected from device")


def main():
    """Main function"""
    print("CallTag Controller - Real Device Test")
    print("=" * 40)
    print(f"Target Device: *************:60000")
    print()
    
    # Create test instance
    test = RealDeviceTest()
    
    try:
        # Setup device
        test.setup_device()
        
        # Connect
        if not test.connect():
            print("Cannot proceed without connection. Please check:")
            print("1. Device is powered on and connected to network")
            print("2. IP address ************* is correct")
            print("3. Port 60000 is correct")
            print("4. No firewall blocking the connection")
            return 1
        
        # Choose test mode
        print("\nChoose test mode:")
        print("1. Basic communication test")
        print("2. Tag calling test")
        print("3. Interactive mode")
        print("4. Full test sequence")
        
        choice = input("Enter choice (1-4): ").strip()
        
        if choice == '1':
            test.test_basic_communication()
        elif choice == '2':
            test.test_tag_calling()
        elif choice == '3':
            test.interactive_mode()
        elif choice == '4':
            print("Running full test sequence...")
            test.test_basic_communication()
            if test.detected_tags:
                test.test_tag_calling()
            test.interactive_mode()
        else:
            print("Invalid choice")
            return 1
    
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        test.disconnect()
    
    print("\n✅ Test completed!")
    return 0


if __name__ == '__main__':
    sys.exit(main())
