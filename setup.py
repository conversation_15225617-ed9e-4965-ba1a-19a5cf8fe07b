#!/usr/bin/env python3
"""
Setup script for CallTag Controller
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open('README.md', 'r', encoding='utf-8') as f:
        return f.read()

# Read version from __init__.py
def get_version():
    version_file = os.path.join('calltag_controller', '__init__.py')
    with open(version_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.startswith('__version__'):
                return line.split('=')[1].strip().strip('"\'')
    return '1.0.0'

setup(
    name='calltag-controller',
    version=get_version(),
    description='Python library for controlling sound-light tag readers',
    long_description=read_readme(),
    long_description_content_type='text/markdown',
    author='AI Assistant',
    author_email='<EMAIL>',
    url='https://github.com/example/calltag-controller',
    packages=find_packages(),
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Operating System :: OS Independent',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.6',
        'Programming Language :: Python :: 3.7',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Topic :: Software Development :: Libraries :: Python Modules',
        'Topic :: System :: Hardware',
        'Topic :: Communications',
    ],
    python_requires='>=3.6',
    install_requires=[
        # Core dependencies are built-in Python modules
    ],
    extras_require={
        'serial': ['pyserial>=3.4'],
        'usb': ['hidapi>=0.10.1'],
        'all': ['pyserial>=3.4', 'hidapi>=0.10.1'],
    },
    entry_points={
        'console_scripts': [
            'calltag-demo=calltag_controller.examples.basic_usage:main',
            'calltag-server=calltag_controller.examples.tcp_server_demo:main',
            'calltag-client=calltag_controller.examples.tcp_client_demo:main',
        ],
    },
    include_package_data=True,
    zip_safe=False,
    keywords='rfid, tag, reader, sound, light, hardware, iot',
    project_urls={
        'Bug Reports': 'https://github.com/example/calltag-controller/issues',
        'Source': 'https://github.com/example/calltag-controller',
        'Documentation': 'https://github.com/example/calltag-controller/blob/main/README.md',
    },
)
