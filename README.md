# CallTag Controller - Python Library

声光标签读卡器Python控制库

A Python library for controlling sound-light tag readers that can be connected via Serial, USB, TCP Client, or TCP Server connections. This library replaces the original C# implementation and provides cross-platform support, including Linux.

## Features

- **Multiple Connection Types**: Serial, USB HID, TCP Client, TCP Server
- **Cross-Platform**: Works on Windows, Linux, and macOS
- **Easy to Use**: Simple API for device control
- **Real-time Tag Detection**: Continuous monitoring with callbacks
- **Tag Management**: Call/stop calling specific tags with sound and light alerts
- **Protocol Implementation**: Full implementation of the device communication protocol

## Installation

### Prerequisites

```bash
# For serial communication
pip install pyserial

# For USB HID communication
pip install hidapi

# Core dependencies (usually included with Python)
# - socket (built-in)
# - threading (built-in)
# - json (built-in)
```

### Install the Library

```bash
# Clone or download the library
git clone <repository-url>
cd calltag_controller

# Install in development mode
pip install -e .

# Or copy the calltag_controller directory to your project
```

## Quick Start

### Serial Connection Example

```python
from calltag_controller import CallTagDevice, SerialConnection
from calltag_controller.core.protocol import Protocol

# Create connection
connection = SerialConnection('/dev/ttyUSB0', 115200)  # Linux
# connection = SerialConnection('COM3', 115200)  # Windows

# Create device
device = CallTagDevice(connection)

# Set up callbacks
def on_tag_detected(tag_info):
    print(f"Tag detected: {tag_info}")

device.on_tag_detected = on_tag_detected

# Connect and start reading
if device.connect():
    device.start_reading()
    
    # Call a specific tag
    device.call_tag(123456, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True)
    
    # Stop calling after some time
    time.sleep(10)
    device.stop_call_tag(123456)
    
    # Stop reading and disconnect
    device.stop_reading()
    device.disconnect()
```

### TCP Client Example

```python
from calltag_controller import CallTagDevice, TCPClientConnection

# Create TCP connection
connection = TCPClientConnection('192.168.1.250', 60000)
device = CallTagDevice(connection)

# Connect and use
if device.connect():
    device.start_reading()
    # ... use device
    device.disconnect()
```

### USB HID Example

```python
from calltag_controller import CallTagDevice, USBConnection

# Create USB connection
connection = USBConnection()

# List available devices
devices = connection.get_available_devices()
print(f"Found {len(devices)} USB devices")

# Connect to first device
device = CallTagDevice(connection)
if device.connect():
    # ... use device
    device.disconnect()
```

### TCP Server Example

```python
from calltag_controller import TCPServerConnection

# Create server
server = TCPServerConnection('127.0.0.1', 60001)

# Set up callbacks
def on_client_connected(address, port):
    print(f"Client connected: {address}:{port}")

def on_data_received(data, address, port):
    print(f"Received data from {address}:{port}")

server.on_client_connected = on_client_connected
server.on_data_received = on_data_received

# Start server
if server.connect():
    # Server is now listening
    # Handle client connections and data
    pass
```

## API Reference

### CallTagDevice

Main device class for controlling CallTag readers.

#### Methods

- `connect()` - Connect to the device
- `disconnect()` - Disconnect from the device
- `start_reading()` - Start reading tags
- `stop_reading()` - Stop reading tags
- `call_tag(tag_id, led_mode, time_mode, buzzer_enable)` - Call a specific tag
- `stop_call_tag(tag_id)` - Stop calling a specific tag
- `get_cached_tags()` - Get all detected tags
- `clear_cache()` - Clear tag cache
- `get_device_info()` - Get device information

#### Callbacks

- `on_tag_detected(tag_info)` - Called when a tag is detected
- `on_connection_lost()` - Called when connection is lost
- `on_error(error_msg)` - Called when an error occurs

### Connection Classes

#### SerialConnection

```python
SerialConnection(port, baudrate=115200, timeout=1.0)
```

#### USBConnection

```python
USBConnection(vendor_id=0x0483, product_id=0x5750)
```

#### TCPClientConnection

```python
TCPClientConnection(host, port, timeout=5.0)
```

#### TCPServerConnection

```python
TCPServerConnection(host='127.0.0.1', port=60001)
```

### Protocol Constants

```python
from calltag_controller.core.protocol import Protocol

# LED Modes
Protocol.LED_RED_ONLY      # 单独红灯
Protocol.LED_GREEN_ONLY    # 单独绿灯
Protocol.LED_CROSS_MODE    # 交叉模式
Protocol.LED_ALTERNATE_MODE # 交替模式
Protocol.LED_FLASH_MODE    # 爆闪模式

# Time Durations
Protocol.TIME_10_SEC       # 10秒
Protocol.TIME_30_SEC       # 30秒
Protocol.TIME_60_SEC       # 60秒
Protocol.TIME_5_MIN        # 5分钟
Protocol.TIME_PERMANENT    # 永久
```

## Examples

The `examples/` directory contains several demonstration scripts:

- `basic_usage.py` - Basic usage examples for all connection types
- `tcp_client_demo.py` - Interactive TCP client demo
- `tcp_server_demo.py` - TCP server simulation demo

Run examples:

```bash
cd calltag_controller/examples
python basic_usage.py
python tcp_client_demo.py
python tcp_server_demo.py
```

## Linux Setup

### Serial Port Permissions

On Linux, you may need to add your user to the dialout group:

```bash
sudo usermod -a -G dialout $USER
# Log out and log back in
```

### USB Device Permissions

For USB HID devices, you may need to set up udev rules:

```bash
# Create udev rule file
sudo nano /etc/udev/rules.d/99-calltag.rules

# Add this line (adjust vendor/product IDs as needed):
SUBSYSTEM=="hidraw", ATTRS{idVendor}=="0483", ATTRS{idProduct}=="5750", MODE="0666"

# Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger
```

## Troubleshooting

### Common Issues

1. **Serial port not found**: Check port name and permissions
2. **USB device not detected**: Check USB permissions and device IDs
3. **TCP connection failed**: Check IP address, port, and firewall settings
4. **Import errors**: Ensure all dependencies are installed

### Debug Mode

Enable debug output by setting environment variable:

```bash
export CALLTAG_DEBUG=1
python your_script.py
```

## Protocol Documentation

The library implements the communication protocol as described in the original documentation:
- Command packet format: `'S' 'W' [LEN_H] [LEN_L] [ADDR] [CMD] [PARAMS...] [CHECKSUM]`
- JSON data format for tag information
- Checksum calculation for data integrity

## License

This library is provided as-is for educational and development purposes.

## Contributing

Contributions are welcome! Please feel free to submit issues and pull requests.
