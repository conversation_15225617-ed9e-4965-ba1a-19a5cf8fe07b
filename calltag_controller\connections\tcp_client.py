"""
TCP Client connection implementation for CallTag devices
TCP客户端连接实现
"""

import socket
import time
from typing import Optional


class TCPClientConnection:
    """
    TCP Client connection for CallTag devices
    声光标签设备的TCP客户端连接
    """
    
    def __init__(self, host: str, port: int, timeout: float = 5.0):
        """
        Initialize TCP client connection
        
        Args:
            host (str): Server IP address
            port (int): Server port number
            timeout (float): Connection timeout in seconds
        """
        self.host = host
        self.port = port
        self.timeout = timeout
        self.socket: Optional[socket.socket] = None
        self.is_connected = False
    
    def connect(self) -> bool:
        """
        Connect to TCP server
        连接TCP服务器
        
        Returns:
            bool: True if connection successful
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            
            # Connect to server
            self.socket.connect((self.host, self.port))
            
            # Set socket to non-blocking mode for receive operations
            self.socket.setblocking(False)
            
            self.is_connected = True
            return True
        except Exception as e:
            print(f"TCP connection failed: {e}")
            if self.socket:
                self.socket.close()
                self.socket = None
            return False
    
    def disconnect(self):
        """
        Disconnect from TCP server
        断开TCP连接
        """
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        self.is_connected = False
    
    def send(self, data: bytes) -> bool:
        """
        Send data through TCP connection
        通过TCP连接发送数据
        
        Args:
            data (bytes): Data to send
            
        Returns:
            bool: True if sent successfully
        """
        if not self.is_connected or not self.socket:
            return False
        
        try:
            # Set socket to blocking mode for send
            self.socket.setblocking(True)
            bytes_sent = self.socket.send(data)
            # Set back to non-blocking
            self.socket.setblocking(False)
            return bytes_sent == len(data)
        except Exception as e:
            print(f"TCP send failed: {e}")
            return False
    
    def receive(self, max_size: int = 4096) -> bytes:
        """
        Receive data from TCP connection
        从TCP连接接收数据
        
        Args:
            max_size (int): Maximum bytes to read
            
        Returns:
            bytes: Received data
        """
        if not self.is_connected or not self.socket:
            return b''
        
        try:
            data = self.socket.recv(max_size)
            return data
        except socket.error as e:
            # Non-blocking socket may raise EAGAIN/EWOULDBLOCK
            if e.errno in (socket.EAGAIN, socket.EWOULDBLOCK):
                return b''
            print(f"TCP receive failed: {e}")
            return b''
        except Exception as e:
            print(f"TCP receive failed: {e}")
            return b''
    
    def is_connection_alive(self) -> bool:
        """
        Check if the TCP connection is still alive
        检查TCP连接是否仍然活跃
        
        Returns:
            bool: True if connection is alive
        """
        if not self.is_connected or not self.socket:
            return False
        
        try:
            # Try to send a zero-length message
            self.socket.send(b'', socket.MSG_DONTWAIT)
            return True
        except socket.error:
            return False
    
    def reconnect(self) -> bool:
        """
        Reconnect to the server
        重新连接服务器
        
        Returns:
            bool: True if reconnection successful
        """
        self.disconnect()
        time.sleep(1)  # Wait a bit before reconnecting
        return self.connect()
    
    def set_keepalive(self, enable: bool = True, idle: int = 1, interval: int = 1, count: int = 3):
        """
        Configure TCP keepalive
        配置TCP保活机制
        
        Args:
            enable (bool): Enable keepalive
            idle (int): Seconds before sending keepalive probes
            interval (int): Interval between keepalive probes
            count (int): Number of failed probes before declaring connection dead
        """
        if not self.socket:
            return
        
        try:
            if enable:
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)
                
                # Platform-specific keepalive settings
                import platform
                if platform.system() == 'Linux':
                    self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPIDLE, idle)
                    self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPINTVL, interval)
                    self.socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_KEEPCNT, count)
                elif platform.system() == 'Windows':
                    # Windows uses different socket options
                    self.socket.ioctl(socket.SIO_KEEPALIVE_VALS, (1, idle * 1000, interval * 1000))
            else:
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 0)
        except Exception as e:
            print(f"Failed to set keepalive: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
