﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace CalltagDemo
{
    //class Extensions
    //{
    //}
    public static partial class Extensions
    {
        #region 数据类型转换
        /// <summary>
        /// 将字节数组以ASCII编码方式转成字符串,并去掉前面补0部分
        /// </summary>
        /// <param name="buffer"></param>
        /// <param name="offset"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static string AsciiBytesToStr(this byte[] buffer, int offset, int length)
        {
            return Encoding.ASCII.GetString(buffer, offset, length).TrimStart('\0');
        }
        /// <summary>
        /// 将字符串以ASCII编码方式转成字节数组,不足长度的部分在前面补0
        /// </summary>
        /// <param name="buffer"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static byte[] AsciiStrToBytes(this string text, int length)
        {
            byte[] buffer = new byte[length];
            byte[] data = Encoding.ASCII.GetBytes(text);
            Array.Copy(data, 0, buffer, length - data.Length, data.Length);
            return buffer;
        }

        /// <summary>
        /// 将字符串以GBK编码转换成字节数组
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static byte[] GbkStrToBytes(this string str)
        {
            byte[] data = Encoding.GetEncoding("GBK").GetBytes(str);
            return data;
        }

        /// <summary>
        /// 将字符串以GBK编码转换成字节数组
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static string GbkBytesToStr(this byte[] buffer)
        {
            return Encoding.GetEncoding("GBK").GetString(buffer);
        }

        /// <summary>
        /// 字节数组转16进制字符串
        /// </summary>
        /// <param name="buffer">数组</param>
        /// <param name="isUpper">是否大写输出</param>
        /// <returns></returns>
        public static string ToHexStr(this byte[] buffer, bool isUpper = true, bool isHex = true)
        {
            StringBuilder str = new StringBuilder();
            for (int i = 0; i < buffer.Length; i++)
            {
                if (isHex)
                    str.Append(buffer[i].ToString("x2"));
                else
                    str.Append(buffer[i].ToString());
            }
            return isUpper ? str.ToString().ToUpper() : str.ToString();
        }
        public static string ToHexStr(this byte[] buffer, int offset, int length, bool isUpper = true, bool isHex = true)
        {
            if ((offset + length) > buffer.Length)
                throw new IndexOutOfRangeException();
            StringBuilder str = new StringBuilder();
            for (int i = offset; i < offset + length; i++)
            {
                if (isHex)
                    str.Append(buffer[i].ToString("x2"));
                else
                    str.Append(buffer[i].ToString());
            }
            return isUpper ? str.ToString().ToUpper() : str.ToString();
        }
        /// <summary>
        /// 16进制字符串转字节数组
        /// </summary>
        /// <param name="strSource"></param>
        /// <returns></returns>
        public static byte[] StrToBytes(this string strSource, bool isHex = true)
        {
            byte[] buffer = new byte[strSource.Length / 2];
            for (int i = 0; i < buffer.Length; i++)
            {
                if (isHex)
                    buffer[i] = Convert.ToByte(strSource.Substring(i * 2, 2), 16);
                else
                    buffer[i] = Convert.ToByte(strSource.Substring(i * 2, 2));
            }
            return buffer;
        }
        /// <summary>
        /// BCD码转为10进制串(阿拉伯数据) 
        /// </summary>
        /// <param name="bytes">BCD码 </param>
        /// <returns>10进制串 </returns>
        public static string BcdBytesToStr(this byte[] bytes)
        {
            StringBuilder temp = new StringBuilder(bytes.Length * 2);

            for (int i = 0; i < bytes.Length; i++)
            {
                temp.Append((byte)((bytes[i] & 0xf0) >> 4));
                temp.Append((byte)(bytes[i] & 0x0f));
            }
            return temp.ToString().TrimStart('0');
            //return temp.ToString().Substring(0, 1).Equals("0") ? temp.ToString().Substring(1) : temp.ToString();
        }

        /// <summary>
        /// 10进制串转为BCD码
        /// </summary>
        /// <param name="asc">10进制数</param>
        /// <param name="len">要输出的字节数组长度</param>
        /// <returns></returns>
        public static byte[] BcdStrToBytes(this string asc, int len)
        {
            int fillLen = len * 2 - asc.Length;
            for (int i = 0; i < fillLen; i++)
            {
                asc = "0" + asc;
            }
            byte[] bbt = new byte[len];
            byte[] abt = Encoding.ASCII.GetBytes(asc);
            int j, k;

            for (int p = 0; p < asc.Length / 2; p++)
            {
                if ((abt[2 * p] >= '0') && (abt[2 * p] <= '9'))
                {
                    j = abt[2 * p] - '0';
                }
                else if ((abt[2 * p] >= 'a') && (abt[2 * p] <= 'z'))
                {
                    j = abt[2 * p] - 'a' + 0x0a;
                }
                else
                {
                    j = abt[2 * p] - 'A' + 0x0a;
                }

                if ((abt[2 * p + 1] >= '0') && (abt[2 * p + 1] <= '9'))
                {
                    k = abt[2 * p + 1] - '0';
                }
                else if ((abt[2 * p + 1] >= 'a') && (abt[2 * p + 1] <= 'z'))
                {
                    k = abt[2 * p + 1] - 'a' + 0x0a;
                }
                else
                {
                    k = abt[2 * p + 1] - 'A' + 0x0a;
                }

                int a = (j << 4) + k;
                byte b = (byte)a;
                bbt[p] = b;
            }
            return bbt;
        }
        #endregion

        #region 字节数组与数值转换
        /// <summary>
        /// 转换单个字节
        /// </summary>
        /// <param name="singleByte"></param>
        /// <returns></returns>
        public static string ToHex(this byte singleByte)
        {
            return string.Format("{0:X2}", singleByte);
        }
        /// <summary>
        /// 将字节数组转成长整型数值
        /// </summary>
        /// <param name="bytes"></param>
        /// <returns></returns>
        public static int BytesToNumber(this byte[] bytes)
        {
            int ret = 0;
            for (int i = 0; i < bytes.Length; i++)
            {
                ret += bytes[i] << (24 - i * 8);
            }
            return ret;
        }
        /// <summary>
        /// 将长整型数转成字节数组
        /// </summary>
        /// <param name="num"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static byte[] NumberToBytes(this int num, int length)
        {
            byte[] bytes = new byte[length];
            for (int i = 0; i < length; i++)
            {
                bytes[i] = (byte)(num >> (24 - i * 8));
            }
            return bytes;
        }
        #endregion

        #region 数值转换
        /// <summary>
        /// 转换为整型
        /// </summary>
        /// <param name="data">数据</param>
        public static int ToInt(this object data)
        {
            if (data == null)
                return 0;
            int result;
            var success = int.TryParse(data.ToString(), out result);
            if (success)
                return result;
            try
            {
                return Convert.ToInt32(ToDouble(data, 0));
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// 转换为可空整型
        /// </summary>
        /// <param name="data">数据</param>
        public static int? ToIntOrNull(this object data)
        {
            if (data == null)
                return null;
            int result;
            bool isValid = int.TryParse(data.ToString(), out result);
            if (isValid)
                return result;
            return null;
        }

        /// <summary>
        /// 转换为双精度浮点数
        /// </summary>
        /// <param name="data">数据</param>
        public static double ToDouble(this object data)
        {
            if (data == null)
                return 0;
            double result;
            return double.TryParse(data.ToString(), out result) ? result : 0;
        }

        /// <summary>
        /// 转换为双精度浮点数,并按指定的小数位4舍5入
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="digits">小数位数</param>
        public static double ToDouble(this object data, int digits)
        {
            return Math.Round(ToDouble(data), digits);
        }

        /// <summary>
        /// 转换为可空双精度浮点数
        /// </summary>
        /// <param name="data">数据</param>
        public static double? ToDoubleOrNull(this object data)
        {
            if (data == null)
                return null;
            double result;
            bool isValid = double.TryParse(data.ToString(), out result);
            if (isValid)
                return result;
            return null;
        }

        /// <summary>
        /// 转换为高精度浮点数
        /// </summary>
        /// <param name="data">数据</param>
        public static decimal ToDecimal(this object data)
        {
            if (data == null)
                return 0;
            decimal result;
            return decimal.TryParse(data.ToString(), out result) ? result : 0;
        }

        /// <summary>
        /// 转换为高精度浮点数,并按指定的小数位4舍5入
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="digits">小数位数</param>
        public static decimal ToDecimal(this object data, int digits)
        {
            return Math.Round(ToDecimal(data), digits);
        }

        /// <summary>
        /// 转换为可空高精度浮点数
        /// </summary>
        /// <param name="data">数据</param>
        public static decimal? ToDecimalOrNull(this object data)
        {
            if (data == null)
                return null;
            decimal result;
            bool isValid = decimal.TryParse(data.ToString(), out result);
            if (isValid)
                return result;
            return null;
        }

        /// <summary>
        /// 转换为可空高精度浮点数,并按指定的小数位4舍5入
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="digits">小数位数</param>
        public static decimal? ToDecimalOrNull(this object data, int digits)
        {
            var result = ToDecimalOrNull(data);
            if (result == null)
                return null;
            return Math.Round(result.Value, digits);
        }

        #endregion

        #region 位运算处理
        /// <summary>
        /// 获取字节的某个bit位
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="digits">小数位数</param>
        public static bool GetBit(this byte data, int bit)
        {
            return (data & (1 << bit)) > 0;
        }
        /// <summary>
        /// 将某个bit位置为1
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="digits">小数位数</param>
        public static byte SetBit(this byte data, int bit)
        {
            return (byte)(data | (1 << bit));
        }

        #endregion

        #region 日期转换
        /// <summary>
        /// 转换为日期
        /// </summary>
        /// <param name="data">数据</param>
        public static DateTime ToDate(this object data)
        {
            if (data == null)
                return DateTime.MinValue;
            DateTime result;
            return DateTime.TryParse(data.ToString(), out result) ? result : DateTime.MinValue;
        }

        /// <summary>
        /// 转换为可空日期
        /// </summary>
        /// <param name="data">数据</param>
        public static DateTime? ToDateOrNull(this object data)
        {
            if (data == null)
                return null;
            DateTime result;
            bool isValid = DateTime.TryParse(data.ToString(), out result);
            if (isValid)
                return result;
            return null;
        }

        #endregion

        #region 布尔转换
        /// <summary>
        /// 转换为布尔值
        /// </summary>
        /// <param name="data">数据</param>
        public static bool ToBool(this object data)
        {
            if (data == null)
                return false;
            bool? value = GetBool(data);
            if (value != null)
                return value.Value;
            bool result;
            return bool.TryParse(data.ToString(), out result) && result;
        }

        /// <summary>
        /// 获取布尔值
        /// </summary>
        private static bool? GetBool(this object data)
        {
            switch (data.ToString().Trim().ToLower())
            {
                case "0":
                    return false;
                case "1":
                    return true;
                case "是":
                    return true;
                case "否":
                    return false;
                case "yes":
                    return true;
                case "no":
                    return false;
                default:
                    return null;
            }
        }

        /// <summary>
        /// 转换为可空布尔值
        /// </summary>
        /// <param name="data">数据</param>
        public static bool? ToBoolOrNull(this object data)
        {
            if (data == null)
                return null;
            bool? value = GetBool(data);
            if (value != null)
                return value.Value;
            bool result;
            bool isValid = bool.TryParse(data.ToString(), out result);
            if (isValid)
                return result;
            return null;
        }

        /// <summary>
        /// 转换为可空布尔值
        /// </summary>
        /// <param name="data">数据</param>
        public static bool? ToBoolDefault(this object data, bool bDefault = false)
        {
            if (data == null)
                return bDefault;
            bool? value = GetBool(data);
            if (value != null)
                return value.Value;
            bool result;
            bool isValid = bool.TryParse(data.ToString(), out result);
            if (isValid)
                return result;
            return bDefault;
        }

        #endregion

        #region 字符串转换
        /// <summary>
        /// 转换为字符串
        /// </summary>
        /// <param name="data">数据</param>
        public static string ToString(this object data)
        {
            return data == null ? string.Empty : data.ToString().Trim();
        }
        #endregion
    }
}
