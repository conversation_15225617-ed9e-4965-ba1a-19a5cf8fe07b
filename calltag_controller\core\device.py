"""
CallTag Device class - Main interface for controlling sound-light tag readers
声光标签设备类 - 控制声光标签读卡器的主要接口
"""

import threading
import time
import json
from typing import List, Optional, Callable, Dict, Any
from .tag_info import TagInfo
from .protocol import Protocol


class CallTagDevice:
    """
    Main device class for controlling CallTag readers
    控制声光标签读卡器的主要设备类
    """
    
    def __init__(self, connection):
        """
        Initialize CallTag device
        
        Args:
            connection: Connection object (Serial, USB, TCP, etc.)
        """
        self.connection = connection
        self.is_connected = False
        self.is_reading = False
        self.tag_cache: Dict[str, TagInfo] = {}
        self.device_info = {}
        
        # Threading
        self._read_thread = None
        self._stop_reading = threading.Event()
        
        # Callbacks
        self.on_tag_detected: Optional[Callable[[TagInfo], None]] = None
        self.on_connection_lost: Optional[Callable[[], None]] = None
        self.on_error: Optional[Callable[[str], None]] = None
    
    def connect(self) -> bool:
        """
        Connect to the device
        连接设备
        
        Returns:
            bool: True if connection successful
        """
        try:
            if self.connection.connect():
                self.is_connected = True
                # Get device information
                self._get_device_info()
                return True
            return False
        except Exception as e:
            if self.on_error:
                self.on_error(f"Connection failed: {str(e)}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the device
        断开设备连接
        """
        self.stop_reading()
        if self.connection:
            self.connection.disconnect()
        self.is_connected = False
    
    def start_reading(self) -> bool:
        """
        Start reading tags from the device
        开始读取标签
        
        Returns:
            bool: True if started successfully
        """
        if not self.is_connected:
            if self.on_error:
                self.on_error("Device not connected")
            return False
        
        if self.is_reading:
            return True
        
        try:
            # Send start read command
            cmd = Protocol.create_start_read_command()
            if not self.connection.send(cmd):
                return False
            
            self.is_reading = True
            self._stop_reading.clear()
            
            # Start reading thread
            self._read_thread = threading.Thread(target=self._read_loop, daemon=True)
            self._read_thread.start()
            
            return True
        except Exception as e:
            if self.on_error:
                self.on_error(f"Failed to start reading: {str(e)}")
            return False
    
    def stop_reading(self):
        """
        Stop reading tags from the device
        停止读取标签
        """
        if not self.is_reading:
            return
        
        try:
            # Send stop read command
            cmd = Protocol.create_stop_read_command()
            self.connection.send(cmd)
            
            # Stop reading thread
            self._stop_reading.set()
            if self._read_thread and self._read_thread.is_alive():
                self._read_thread.join(timeout=2.0)
            
            self.is_reading = False
        except Exception as e:
            if self.on_error:
                self.on_error(f"Failed to stop reading: {str(e)}")
    
    def call_tag(self, tag_id: int, led_mode: int = Protocol.LED_FLASH_MODE, 
                 time_mode: int = Protocol.TIME_30_SEC, buzzer_enable: bool = True) -> bool:
        """
        Call a specific tag (activate sound and light)
        呼叫指定标签（激活声光报警）
        
        Args:
            tag_id (int): Tag ID to call
            led_mode (int): LED mode (0-4)
            time_mode (int): Duration mode (0-4)
            buzzer_enable (bool): Enable buzzer
            
        Returns:
            bool: True if command sent successfully
        """
        if not self.is_connected:
            if self.on_error:
                self.on_error("Device not connected")
            return False
        
        try:
            cmd = Protocol.create_call_tag_command(1, tag_id, led_mode, time_mode, buzzer_enable)
            return self.connection.send(cmd)
        except Exception as e:
            if self.on_error:
                self.on_error(f"Failed to call tag: {str(e)}")
            return False
    
    def stop_call_tag(self, tag_id: int) -> bool:
        """
        Stop calling a specific tag
        停止呼叫指定标签
        
        Args:
            tag_id (int): Tag ID to stop calling
            
        Returns:
            bool: True if command sent successfully
        """
        if not self.is_connected:
            if self.on_error:
                self.on_error("Device not connected")
            return False
        
        try:
            cmd = Protocol.create_stop_call_command(1, tag_id)
            return self.connection.send(cmd)
        except Exception as e:
            if self.on_error:
                self.on_error(f"Failed to stop calling tag: {str(e)}")
            return False
    
    def get_cached_tags(self) -> List[TagInfo]:
        """
        Get all cached tag information
        获取所有缓存的标签信息
        
        Returns:
            List[TagInfo]: List of cached tags
        """
        return list(self.tag_cache.values())
    
    def clear_cache(self):
        """
        Clear the tag cache
        清空标签缓存
        """
        self.tag_cache.clear()
    
    def get_device_info(self) -> dict:
        """
        Get device information
        获取设备信息
        
        Returns:
            dict: Device information
        """
        return self.device_info.copy()
    
    def _get_device_info(self):
        """
        Internal method to retrieve device information
        内部方法：获取设备信息
        """
        # This would typically send a command to get device info
        # For now, we'll set some default values
        self.device_info = {
            'software_version': '1.0',
            'hardware_version': '1.0',
            'serial_number': 'Unknown',
            'device_type': 'CallTag Reader'
        }
    
    def _read_loop(self):
        """
        Main reading loop running in separate thread
        在独立线程中运行的主要读取循环
        """
        while not self._stop_reading.is_set():
            try:
                # Send inquiry command periodically
                cmd = Protocol.create_inquiry_command()
                if not self.connection.send(cmd):
                    break
                
                # Wait for response
                time.sleep(0.1)
                
                # Read response
                data = self.connection.receive()
                if data:
                    self._process_response(data)
                
                # Wait before next inquiry
                time.sleep(0.5)
                
            except Exception as e:
                if self.on_error:
                    self.on_error(f"Error in read loop: {str(e)}")
                break
    
    def _process_response(self, data: bytes):
        """
        Process response data from device
        处理设备响应数据
        
        Args:
            data (bytes): Raw response data
        """
        try:
            # Try to parse as protocol response
            success, parsed = Protocol.parse_response(data)
            if success and parsed:
                payload = parsed['payload']
                # Try to decode as JSON
                try:
                    json_str = payload.decode('utf-8', errors='ignore')
                    if json_str.strip():
                        tags = Protocol.parse_tag_data(json_str)
                        self._update_tag_cache(tags)
                except UnicodeDecodeError:
                    pass
            else:
                # Try to parse as direct JSON
                try:
                    json_str = data.decode('utf-8', errors='ignore')
                    if json_str.strip():
                        tags = Protocol.parse_tag_data(json_str)
                        self._update_tag_cache(tags)
                except UnicodeDecodeError:
                    pass
        except Exception as e:
            if self.on_error:
                self.on_error(f"Error processing response: {str(e)}")
    
    def _update_tag_cache(self, tags: List[TagInfo]):
        """
        Update the tag cache with new tag data
        使用新的标签数据更新缓存
        
        Args:
            tags (List[TagInfo]): New tag data
        """
        for tag in tags:
            if tag.id in self.tag_cache:
                # Update existing tag
                self.tag_cache[tag.id].increment_count()
                self.tag_cache[tag.id].update_state(tag.state)
            else:
                # Add new tag
                self.tag_cache[tag.id] = tag
            
            # Notify callback
            if self.on_tag_detected:
                self.on_tag_detected(self.tag_cache[tag.id])
