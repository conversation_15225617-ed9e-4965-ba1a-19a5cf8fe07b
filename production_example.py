#!/usr/bin/env python3
"""
Production example for CallTag Controller
生产环境使用示例

This example shows how to use the CallTag Controller library in a production environment.
"""

import time
import logging
import threading
from typing import Dict, List, Optional
from datetime import datetime

from calltag_controller import CallTagDevice, TCPClientConnection
from calltag_controller.core.protocol import Protocol
from calltag_controller.core.tag_info import TagInfo


class CallTagManager:
    """
    Production-ready CallTag manager
    生产环境声光标签管理器
    """
    
    def __init__(self, device_ip: str = "*************", device_port: int = 60000):
        self.device_ip = device_ip
        self.device_port = device_port
        self.device: Optional[CallTagDevice] = None
        self.is_running = False
        
        # Tag management
        self.active_tags: Dict[str, TagInfo] = {}
        self.called_tags: Dict[int, datetime] = {}
        
        # Threading
        self.monitor_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # Setup logging
        self.setup_logging()
        
        # Callbacks
        self.on_tag_found_callback = None
        self.on_tag_lost_callback = None
        self.on_device_error_callback = None
    
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('calltag_manager.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('CallTagManager')
    
    def connect(self) -> bool:
        """
        Connect to the CallTag device
        连接到声光标签设备
        """
        try:
            self.logger.info(f"Connecting to CallTag device at {self.device_ip}:{self.device_port}")
            
            connection = TCPClientConnection(self.device_ip, self.device_port, timeout=10.0)
            self.device = CallTagDevice(connection)
            
            # Set up callbacks
            self.device.on_tag_detected = self._on_tag_detected
            self.device.on_error = self._on_device_error
            self.device.on_connection_lost = self._on_connection_lost
            
            if self.device.connect():
                self.logger.info("Successfully connected to CallTag device")
                return True
            else:
                self.logger.error("Failed to connect to CallTag device")
                return False
                
        except Exception as e:
            self.logger.error(f"Connection error: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from the CallTag device
        断开设备连接
        """
        self.stop_monitoring()
        
        if self.device:
            self.device.disconnect()
            self.logger.info("Disconnected from CallTag device")
    
    def start_monitoring(self) -> bool:
        """
        Start monitoring for tags
        开始监控标签
        """
        if not self.device or not self.device.is_connected:
            self.logger.error("Device not connected")
            return False
        
        try:
            if self.device.start_reading():
                self.is_running = True
                self.stop_event.clear()
                
                # Start monitoring thread
                self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
                self.monitor_thread.start()
                
                self.logger.info("Started tag monitoring")
                return True
            else:
                self.logger.error("Failed to start tag reading")
                return False
                
        except Exception as e:
            self.logger.error(f"Error starting monitoring: {e}")
            return False
    
    def stop_monitoring(self):
        """
        Stop monitoring for tags
        停止监控标签
        """
        if self.is_running:
            self.is_running = False
            self.stop_event.set()
            
            if self.device:
                self.device.stop_reading()
            
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5.0)
            
            self.logger.info("Stopped tag monitoring")
    
    def call_tag(self, tag_id: int, duration: int = Protocol.TIME_30_SEC, 
                 led_mode: int = Protocol.LED_FLASH_MODE, buzzer: bool = True) -> bool:
        """
        Call a specific tag
        呼叫指定标签
        
        Args:
            tag_id: Tag ID to call
            duration: Call duration
            led_mode: LED flash mode
            buzzer: Enable buzzer
        """
        if not self.device or not self.device.is_connected:
            self.logger.error("Device not connected")
            return False
        
        try:
            if self.device.call_tag(tag_id, led_mode, duration, buzzer):
                self.called_tags[tag_id] = datetime.now()
                self.logger.info(f"Called tag {tag_id}")
                return True
            else:
                self.logger.error(f"Failed to call tag {tag_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error calling tag {tag_id}: {e}")
            return False
    
    def stop_call_tag(self, tag_id: int) -> bool:
        """
        Stop calling a specific tag
        停止呼叫指定标签
        """
        if not self.device or not self.device.is_connected:
            self.logger.error("Device not connected")
            return False
        
        try:
            if self.device.stop_call_tag(tag_id):
                if tag_id in self.called_tags:
                    del self.called_tags[tag_id]
                self.logger.info(f"Stopped calling tag {tag_id}")
                return True
            else:
                self.logger.error(f"Failed to stop calling tag {tag_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error stopping call for tag {tag_id}: {e}")
            return False
    
    def get_active_tags(self) -> List[TagInfo]:
        """
        Get list of currently active tags
        获取当前活跃标签列表
        """
        return list(self.active_tags.values())
    
    def get_called_tags(self) -> Dict[int, datetime]:
        """
        Get list of currently called tags
        获取当前被呼叫的标签列表
        """
        return self.called_tags.copy()
    
    def is_tag_active(self, tag_id: str) -> bool:
        """
        Check if a tag is currently active
        检查标签是否活跃
        """
        return tag_id in self.active_tags
    
    def is_tag_called(self, tag_id: int) -> bool:
        """
        Check if a tag is currently being called
        检查标签是否正在被呼叫
        """
        return tag_id in self.called_tags
    
    def _on_tag_detected(self, tag_info: TagInfo):
        """Internal callback for tag detection"""
        tag_id = tag_info.id
        
        # Check if this is a new tag
        is_new = tag_id not in self.active_tags
        
        # Update active tags
        self.active_tags[tag_id] = tag_info
        
        if is_new:
            self.logger.info(f"New tag detected: {tag_id}")
            if self.on_tag_found_callback:
                self.on_tag_found_callback(tag_info)
        else:
            self.logger.debug(f"Tag update: {tag_id} (count: {tag_info.num})")
    
    def _on_device_error(self, error_msg: str):
        """Internal callback for device errors"""
        self.logger.error(f"Device error: {error_msg}")
        if self.on_device_error_callback:
            self.on_device_error_callback(error_msg)
    
    def _on_connection_lost(self):
        """Internal callback for connection loss"""
        self.logger.warning("Connection to device lost")
        self.is_running = False
        if self.on_device_error_callback:
            self.on_device_error_callback("Connection lost")
    
    def _monitor_loop(self):
        """Internal monitoring loop"""
        last_cleanup = time.time()
        
        while not self.stop_event.is_set():
            try:
                # Periodic cleanup of inactive tags
                current_time = time.time()
                if current_time - last_cleanup > 30:  # Cleanup every 30 seconds
                    self._cleanup_inactive_tags()
                    last_cleanup = current_time
                
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"Error in monitor loop: {e}")
                break
    
    def _cleanup_inactive_tags(self):
        """Remove tags that haven't been seen recently"""
        # This is a simple implementation - in production you might want more sophisticated logic
        pass


# Example usage
def example_usage():
    """Example of how to use the CallTagManager"""
    
    # Create manager
    manager = CallTagManager("*************", 60000)
    
    # Set up callbacks
    def on_tag_found(tag_info):
        print(f"🏷️  New tag found: {tag_info.id}")
        
        # Example: automatically call certain tags
        tag_id = int(tag_info.id) if tag_info.id.isdigit() else 0
        if tag_id in [2654, 399]:  # Your specific tags
            print(f"🔊 Auto-calling known tag: {tag_id}")
            manager.call_tag(tag_id)
    
    def on_error(error_msg):
        print(f"❌ Error: {error_msg}")
    
    manager.on_tag_found_callback = on_tag_found
    manager.on_device_error_callback = on_error
    
    try:
        # Connect and start monitoring
        if manager.connect():
            if manager.start_monitoring():
                print("✅ CallTag system started successfully")
                
                # Run for a while
                time.sleep(30)
                
                # Example: call specific tags
                print("🔊 Calling tag 2654...")
                manager.call_tag(2654)
                
                time.sleep(10)
                
                print("⏹️  Stopping call...")
                manager.stop_call_tag(2654)
                
                # Show status
                active_tags = manager.get_active_tags()
                called_tags = manager.get_called_tags()
                
                print(f"📊 Status:")
                print(f"   Active tags: {len(active_tags)}")
                print(f"   Called tags: {len(called_tags)}")
                
            else:
                print("❌ Failed to start monitoring")
        else:
            print("❌ Failed to connect to device")
    
    except KeyboardInterrupt:
        print("\n⏹️  Shutting down...")
    
    finally:
        manager.disconnect()


if __name__ == '__main__':
    example_usage()
