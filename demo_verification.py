#!/usr/bin/env python3
"""
Demo verification script for CallTag Controller
验证演示脚本
"""

import time
import threading
import sys
import json
from calltag_controller import CallTagDevice, TCPServerConnection, TCPClientConnection
from calltag_controller.core.protocol import Protocol
from calltag_controller.core.tag_info import TagInfo


class MockCallTagServer:
    """Mock CallTag server for testing"""
    
    def __init__(self, host='127.0.0.1', port=60001):
        self.server = TCPServerConnection(host, port)
        self.device_sn = "FF24062720B6"
        self.running = False
        
        # Mock tag data
        self.mock_tags = [
            TagInfo("0000123456", 0x01, 1, self.device_sn),
            TagInfo("0000789012", 0x02, 1, self.device_sn),
        ]
        
        # Set up callbacks
        self.server.on_client_connected = self.on_client_connected
        self.server.on_client_disconnected = self.on_client_disconnected
        self.server.on_data_received = self.on_data_received
    
    def start(self):
        """Start the mock server"""
        print("Starting mock CallTag server...")
        if self.server.connect():
            self.running = True
            print(f"Mock server started on {self.server.host}:{self.server.port}")
            return True
        return False
    
    def stop(self):
        """Stop the mock server"""
        print("Stopping mock server...")
        self.running = False
        self.server.disconnect()
        print("Mock server stopped")
    
    def on_client_connected(self, address, port):
        print(f"[SERVER] Client connected: {address}:{port}")
    
    def on_client_disconnected(self, address, port):
        print(f"[SERVER] Client disconnected: {address}:{port}")
    
    def on_data_received(self, data, address, port):
        print(f"[SERVER] Received from {address}:{port}: {data.hex()}")
        
        # Parse command
        success, parsed = Protocol.parse_response(data)
        if success and parsed:
            cmd = parsed['command']
            addr = parsed['address']
            
            if cmd == Protocol.CMD_INQUIRY:
                # Send mock tag data
                self.send_mock_tag_data(addr)
            elif cmd == Protocol.CMD_CALL_TAG:
                payload = parsed['payload']
                if len(payload) >= 4:
                    tag_id = int.from_bytes(payload[:4], 'big')
                    print(f"[SERVER] Call tag command for ID: {tag_id}")
                self.send_ack(addr)
            elif cmd == Protocol.CMD_STOP_CALL_TAG:
                payload = parsed['payload']
                if len(payload) >= 4:
                    tag_id = int.from_bytes(payload[:4], 'big')
                    print(f"[SERVER] Stop call tag command for ID: {tag_id}")
                self.send_ack(addr)
            else:
                print(f"[SERVER] Unknown command: 0x{cmd:02X}")
    
    def send_mock_tag_data(self, addr):
        """Send mock tag data as JSON"""
        # Create JSON response
        attence_list = []
        for tag in self.mock_tags:
            attence_list.append({
                'ID': f"{int(tag.id):08X}",
                'State': f"{tag.state:02X}"
            })
        
        response = {
            'DevSN': self.device_sn,
            'AttenceID': attence_list
        }
        
        json_str = json.dumps(response)
        response_bytes = json_str.encode('utf-8')
        
        # Send as protocol packet
        packet = Protocol.create_command_packet(addr, Protocol.CMD_INQUIRY, response_bytes)
        self.server.send(packet)
        print(f"[SERVER] Sent tag data: {json_str}")
    
    def send_ack(self, addr):
        """Send acknowledgment"""
        ack_packet = Protocol.create_command_packet(addr, 0x80)  # ACK
        self.server.send(ack_packet)
        print(f"[SERVER] Sent ACK")


def test_tcp_communication():
    """Test TCP client-server communication"""
    print("=== Testing TCP Communication ===")
    
    # Start mock server
    server = MockCallTagServer()
    if not server.start():
        print("Failed to start mock server")
        return False
    
    try:
        # Give server time to start
        time.sleep(1)
        
        # Create client
        client_conn = TCPClientConnection('127.0.0.1', 60001)
        device = CallTagDevice(client_conn)
        
        # Set up client callbacks
        detected_tags = []
        
        def on_tag_detected(tag_info):
            print(f"[CLIENT] Tag detected: {tag_info}")
            detected_tags.append(tag_info)
        
        def on_error(error_msg):
            print(f"[CLIENT] Error: {error_msg}")
        
        device.on_tag_detected = on_tag_detected
        device.on_error = on_error
        
        # Connect client
        print("\n[CLIENT] Connecting to server...")
        if device.connect():
            print("[CLIENT] Connected successfully!")
            
            # Start reading
            print("[CLIENT] Starting to read tags...")
            if device.start_reading():
                print("[CLIENT] Reading started")
                
                # Wait for some tag detections
                time.sleep(3)
                
                # Test calling a tag
                print("[CLIENT] Calling tag 123456...")
                device.call_tag(123456, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True)
                
                time.sleep(2)
                
                # Stop calling
                print("[CLIENT] Stopping call...")
                device.stop_call_tag(123456)
                
                time.sleep(1)
                
                # Stop reading
                device.stop_reading()
                print("[CLIENT] Stopped reading")
                
                # Show results
                print(f"\n[CLIENT] Detected {len(detected_tags)} tags:")
                for tag in detected_tags:
                    print(f"  {tag}")
            
            device.disconnect()
            print("[CLIENT] Disconnected")
        else:
            print("[CLIENT] Failed to connect")
            return False
    
    finally:
        server.stop()
    
    return len(detected_tags) > 0


def test_protocol_functions():
    """Test protocol encoding/decoding"""
    print("\n=== Testing Protocol Functions ===")
    
    # Test command creation
    commands = [
        ("Start Read", Protocol.create_start_read_command(1)),
        ("Stop Read", Protocol.create_stop_read_command(1)),
        ("Inquiry", Protocol.create_inquiry_command(1)),
        ("Call Tag", Protocol.create_call_tag_command(1, 123456, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True)),
        ("Stop Call", Protocol.create_stop_call_command(1, 123456)),
    ]
    
    for name, cmd in commands:
        print(f"{name}: {cmd.hex()}")
        
        # Test parsing
        success, parsed = Protocol.parse_response(cmd)
        if success:
            print(f"  Parsed: Address={parsed['address']}, Command=0x{parsed['command']:02X}")
        else:
            print(f"  Parse failed (expected for some commands)")
    
    return True


def test_json_handling():
    """Test JSON data handling"""
    print("\n=== Testing JSON Handling ===")
    
    # Test JSON parsing
    test_json = '''
    {
        "DevSN": "FF24062720B6",
        "AttenceID": [
            {"ID": "0001E240", "State": "01"},
            {"ID": "000C0FFE", "State": "02"}
        ]
    }
    '''
    
    tags = Protocol.parse_tag_data(test_json)
    print(f"Parsed {len(tags)} tags from JSON:")
    for tag in tags:
        print(f"  {tag}")
    
    # Test JSON creation
    from calltag_controller.utils.helpers import create_json_response
    json_response = create_json_response("FF24062720B6", tags)
    print(f"Created JSON: {json_response}")
    
    return len(tags) > 0


def main():
    """Main verification function"""
    print("CallTag Controller Verification")
    print("=" * 40)
    
    tests = [
        ("Protocol Functions", test_protocol_functions),
        ("JSON Handling", test_json_handling),
        ("TCP Communication", test_tcp_communication),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name}...")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"{test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"{test_name}: FAILED with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("Verification Summary:")
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n✅ All tests passed! The CallTag Controller library is working correctly.")
        print("\nYou can now use the library to control your CallTag devices:")
        print("  - For serial connection: Use SerialConnection with appropriate COM port")
        print("  - For TCP connection: Use TCPClientConnection with device IP and port")
        print("  - For USB connection: Install hidapi and use USBConnection")
        return 0
    else:
        print(f"\n❌ {len(results) - passed} tests failed. Please check the errors above.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
