#!/usr/bin/env python3
"""
Debug device communication to understand the real protocol
调试设备通信以了解真实协议
"""

import time
import sys
import socket
from calltag_controller.connections.tcp_client import TCPClientConnection
from calltag_controller.core.protocol import Protocol


class DeviceCommunicationDebugger:
    """Device communication debugger"""
    
    def __init__(self, host="*************", port=60000):
        self.host = host
        self.port = port
        self.connection = None
        self.raw_socket = None
    
    def connect_raw_socket(self):
        """Connect using raw socket for maximum control"""
        try:
            print(f"🔌 Connecting raw socket to {self.host}:{self.port}")
            self.raw_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.raw_socket.settimeout(10.0)
            self.raw_socket.connect((self.host, self.port))
            print("✅ Raw socket connected")
            return True
        except Exception as e:
            print(f"❌ Raw socket connection failed: {e}")
            return False
    
    def connect_library(self):
        """Connect using our library"""
        try:
            print(f"🔌 Connecting via library to {self.host}:{self.port}")
            self.connection = TCPClientConnection(self.host, self.port, timeout=10.0)
            if self.connection.connect():
                print("✅ Library connection successful")
                return True
            else:
                print("❌ Library connection failed")
                return False
        except Exception as e:
            print(f"❌ Library connection error: {e}")
            return False
    
    def send_raw_command(self, data, description=""):
        """Send command via raw socket"""
        if not self.raw_socket:
            print("❌ Raw socket not connected")
            return None
        
        try:
            print(f"\n📤 [RAW] Sending {description}: {data.hex()}")
            self.raw_socket.send(data)
            
            # Try to receive response with different timeouts
            responses = []
            
            # Quick response (100ms)
            self.raw_socket.settimeout(0.1)
            try:
                response = self.raw_socket.recv(4096)
                if response:
                    responses.append(("Quick", response))
            except socket.timeout:
                pass
            
            # Medium response (500ms)
            self.raw_socket.settimeout(0.5)
            try:
                response = self.raw_socket.recv(4096)
                if response:
                    responses.append(("Medium", response))
            except socket.timeout:
                pass
            
            # Slow response (2s)
            self.raw_socket.settimeout(2.0)
            try:
                response = self.raw_socket.recv(4096)
                if response:
                    responses.append(("Slow", response))
            except socket.timeout:
                pass
            
            # Process responses
            for timing, response in responses:
                print(f"📥 [RAW] {timing} response ({len(response)} bytes): {response.hex()}")
                self._analyze_response(response)
            
            if not responses:
                print("📭 [RAW] No response received")
            
            return responses
            
        except Exception as e:
            print(f"❌ [RAW] Error: {e}")
            return None
    
    def send_library_command(self, data, description=""):
        """Send command via library"""
        if not self.connection:
            print("❌ Library not connected")
            return None
        
        try:
            print(f"\n📤 [LIB] Sending {description}: {data.hex()}")
            if self.connection.send(data):
                print("✅ [LIB] Command sent")
                
                # Try multiple receives
                for i in range(3):
                    time.sleep(0.2)
                    response = self.connection.receive()
                    if response:
                        print(f"📥 [LIB] Response {i+1} ({len(response)} bytes): {response.hex()}")
                        self._analyze_response(response)
                        return response
                
                print("📭 [LIB] No response received")
            else:
                print("❌ [LIB] Failed to send command")
            
        except Exception as e:
            print(f"❌ [LIB] Error: {e}")
        
        return None
    
    def _analyze_response(self, response):
        """Analyze response data"""
        try:
            # Try to decode as text
            text = response.decode('utf-8', errors='ignore').strip()
            if text:
                print(f"📄 As text: {text}")
                
                # Check for JSON
                if '{' in text and '}' in text:
                    print("🔍 Looks like JSON data")
                    try:
                        import json
                        json_data = json.loads(text)
                        print(f"✅ Valid JSON: {json_data}")
                    except:
                        print("❌ Invalid JSON format")
            
            # Try to parse as protocol
            success, parsed = Protocol.parse_response(response)
            if success:
                print(f"📋 Protocol parsed: Address={parsed['address']}, Command=0x{parsed['command']:02X}")
                if parsed['payload']:
                    print(f"📦 Payload ({len(parsed['payload'])} bytes): {parsed['payload'].hex()}")
            
            # Look for patterns
            if len(response) >= 2:
                if response[:2] == b'SW':
                    print("🔍 Starts with 'SW' - looks like our protocol")
                elif response[0:1] == b'{':
                    print("🔍 Starts with '{' - looks like JSON")
                elif response[0:1] == b'\x53':  # 'S' in hex
                    print("🔍 Starts with 0x53 - might be protocol header")
        
        except Exception as e:
            print(f"❌ Analysis error: {e}")
    
    def test_original_commands(self):
        """Test commands from original C# code"""
        print("\n=== Testing Original Commands ===")
        
        commands = [
            # From original C# SeverOperation.cs
            (b'SW\x00\x03\x01\x41\x11', "Start Read (0x41)"),
            (b'SW\x00\x03\x01\x43\x0F', "Inquiry (0x43)"),
            (b'SW\x00\x03\x01\x40\x12', "Stop Read (0x40)"),
        ]
        
        for cmd, desc in commands:
            if self.raw_socket:
                self.send_raw_command(cmd, desc)
            if self.connection:
                self.send_library_command(cmd, desc)
            time.sleep(1)
    
    def test_continuous_inquiry(self, count=5):
        """Test continuous inquiry like original software"""
        print(f"\n=== Testing Continuous Inquiry ({count} times) ===")
        
        # Send start read first
        start_cmd = b'SW\x00\x03\x01\x41\x11'
        if self.raw_socket:
            self.send_raw_command(start_cmd, "Start Read")
        
        time.sleep(1)
        
        # Send multiple inquiries
        inquiry_cmd = b'SW\x00\x03\x01\x43\x0F'
        for i in range(count):
            print(f"\n--- Inquiry {i+1}/{count} ---")
            if self.raw_socket:
                self.send_raw_command(inquiry_cmd, f"Inquiry {i+1}")
            time.sleep(2)  # Wait 2 seconds between inquiries
    
    def test_call_commands(self):
        """Test calling the known tags"""
        print("\n=== Testing Call Commands ===")
        
        # Test calling tag 2654
        tag_id = 2654
        call_cmd = Protocol.create_call_tag_command(
            1, tag_id, 
            Protocol.LED_FLASH_MODE, 
            Protocol.TIME_10_SEC, 
            True
        )
        
        print(f"🔊 Testing call for tag {tag_id}")
        if self.raw_socket:
            self.send_raw_command(call_cmd, f"Call Tag {tag_id}")
        
        time.sleep(3)
        
        # Stop call
        stop_cmd = Protocol.create_stop_call_command(1, tag_id)
        if self.raw_socket:
            self.send_raw_command(stop_cmd, f"Stop Call Tag {tag_id}")
    
    def monitor_traffic(self, duration=30):
        """Monitor all traffic for specified duration"""
        print(f"\n=== Monitoring Traffic for {duration} seconds ===")
        
        if not self.raw_socket:
            print("❌ Need raw socket for monitoring")
            return
        
        # Send start read
        start_cmd = b'SW\x00\x03\x01\x41\x11'
        self.send_raw_command(start_cmd, "Start Read")
        
        # Monitor for responses
        self.raw_socket.settimeout(1.0)
        start_time = time.time()
        packet_count = 0
        
        while time.time() - start_time < duration:
            try:
                response = self.raw_socket.recv(4096)
                if response:
                    packet_count += 1
                    elapsed = int(time.time() - start_time)
                    print(f"\n📦 Packet {packet_count} at {elapsed}s ({len(response)} bytes): {response.hex()}")
                    self._analyze_response(response)
            except socket.timeout:
                # Send periodic inquiry
                if int(time.time() - start_time) % 5 == 0:  # Every 5 seconds
                    inquiry_cmd = b'SW\x00\x03\x01\x43\x0F'
                    print(f"\n📤 Sending periodic inquiry...")
                    self.raw_socket.send(inquiry_cmd)
        
        print(f"\n📊 Monitoring complete. Received {packet_count} packets.")
    
    def disconnect(self):
        """Disconnect all connections"""
        if self.raw_socket:
            try:
                self.raw_socket.close()
            except:
                pass
            print("🔌 Raw socket disconnected")
        
        if self.connection:
            self.connection.disconnect()
            print("🔌 Library connection disconnected")


def main():
    """Main function"""
    print("CallTag Device Communication Debugger")
    print("=" * 50)
    
    debugger = DeviceCommunicationDebugger()
    
    try:
        # Connect both ways
        raw_ok = debugger.connect_raw_socket()
        lib_ok = debugger.connect_library()
        
        if not (raw_ok or lib_ok):
            print("❌ No connections available")
            return 1
        
        print("\nChoose test:")
        print("1. Test original commands")
        print("2. Test continuous inquiry")
        print("3. Test call commands")
        print("4. Monitor traffic")
        print("5. All tests")
        
        choice = input("Enter choice (1-5): ").strip()
        
        if choice == '1':
            debugger.test_original_commands()
        elif choice == '2':
            debugger.test_continuous_inquiry()
        elif choice == '3':
            debugger.test_call_commands()
        elif choice == '4':
            debugger.monitor_traffic()
        elif choice == '5':
            debugger.test_original_commands()
            debugger.test_continuous_inquiry()
            debugger.test_call_commands()
        else:
            print("Invalid choice")
            return 1
    
    except KeyboardInterrupt:
        print("\n\nDebugging interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        debugger.disconnect()
    
    return 0


if __name__ == '__main__':
    sys.exit(main())
