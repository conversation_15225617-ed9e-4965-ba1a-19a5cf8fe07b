﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;

namespace RFID
{
    public class SWComApi
    {
        /******** 功能：串口函数:打开设备 *******************************/
        //  参数：pcCom：串口号，取值为"COM1" , "COM2" ....
        //        iBaudRate：为通讯波特率4800～115200
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_OpenDevice(string pcCom, int iBaudRate);

        /******** 功能：串口函数:关闭串口 *******************************/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_CloseDevice();

        /******** 功能：串口函数:重启设备**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_RebootDevice();

        /******** 功能：串口函数:开始读卡**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_StartRead();

        /******** 功能：串口函数:停止读卡**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_StopRead();

        /******** 功能：串口函数:得到设备信息. 9个字节**********/
        //  参数：pucSystemInfo：返回的设备信息  9个字节, 第一个字节为 固件版本， 第二个字节为硬件版本， 第3个-第9个为产品序列号
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_GetDeviceSystemInfo(byte[] pucSystemInfo);

        /******** 功能：串口函数:得到设备参数**********/
        //  参数：pucDeviceType： 返回的设备类型, 具体见参数手册			
        //        pucDeviceParam：返回的设备参数, 具体见参数手册	
        //        ucParamLength： 返回的参数长度 
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_ReadDeviceParam(out byte pucDeviceType, byte[] pucDeviceParam, byte ucParamLength);

        /******** 功能：串口函数:设置设备参数**********/
        //  参数：ucDeviceType：  设备类型, 必须与设备匹配才能设置成功			
        //        pucDeviceParam：设备参数, 具体见参数手册	
        //        ucParamLength： 参数长度 
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_SetDeviceParam(byte pucDeviceType, byte[] pucDeviceParam, byte ucParamLength);

        /******** 功能：串口函数:恢复默认设备参数**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_DefaultDeviceParam();


        /******** 功能：串口函数:读设备设备网络参数**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_ReadDeviceNetParam(out byte pucDeviceType, byte[] pucDeviceParam, byte ucParamLength);


        /******** 功能：串口函数:设置网络参数**********/
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_SetDeviceNetParam(byte pucDeviceType, byte[] pucDeviceParam, byte ucParamLength);


        /******** 功能：HID函数:读取设备时间**********/
        //  参数：pucDeviceTime：6个字节的时间 分别为 年 月 日 时 分 秒  16进制格式	
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_ReadDeviceTime(byte[] pucDeviceTime);

        /******** 功能：串口函数:设置设备时间**********/
        //  参数：pucDeviceTime：6个字节的时间 分别为 年 月 日 时 分 秒  16进制格式	
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_SetDeviceTime(byte[] pucDeviceTime);

        /******** 功能：串口函数:读取白名单**********/
        //  参数：pucWhiteList：4个字节一组的 ID号
        //        piWhiteListNum：ID号个数
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_ReadWhiteList(byte[] pucWhiteList, out UInt16 piWhiteListNum);

        /******** 功能：串口函数:设置白名单**********/
        //  参数：pucWhiteList：4个字节一组的 ID号
        //        piWhiteListNum：ID号个数
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_WriteWhiteList(byte[] pucWhiteList, UInt16 iWhiteListNum);

        /******** 功能：串口函数:删除所有白名单**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_DeleteAllWhiteList();

        /******** 功能：串口函数:删除单条白名单**********/
        //  参数：pucWhiteList: 4个字节一组的 ID号
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_DeleteOneWhiteList(byte[] pucWhiteList);

        /******** 功能：串口函数:获取缓存卡号的数据，命令方式用**********/
        //  参数：hDeviceHandle：设备句柄
        //        pucDevCache：   缓存数据, 以JSON格式      
        //        piDevCacheNumber：数据总条数
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_GetDevCache(byte[] pucDevJsonCache, out UInt16 piDevCacheLength);

        /******** 功能：串口函数:删除缓存数据**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_DelDevCacheCount();

        /******** 功能：串口函数:初始化回调函数**********/
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_InitCallBack();

        /******** 功能：串口函数:释放回调函数，关闭回调函数后必须释放，否则内存泄漏**********/
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_ReleaseCallBack();

        /******** 功能：串口函数:开启回调函数的接收线程**********/
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_StartRecvThread();

        //添加模块参数设置
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool  SWCom_SetMoudleDeviceSystemInfo(byte[] pucSystemInfo);

        // 搜索指令;
        // 20230426 添加开始搜索/停止搜索
        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_FindID(byte[] pucDeviceID, byte s_buz_led, byte s_time);

        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_DisFindID(byte[] pucDeviceID);

        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWCom_HardChoice(byte temp);
        /*
        * 回调函数原型
        * msg == 1： 表示设备被拔出
        * msg == 2：param1表示答案条数，param2表示答案字符串
        * param2以JSON格式存在 例如 {"DevSN":"230C170406443C","SchoolID":"0000","DevTime":"2017-01-01 18:00:29","AnswerKey":[{"ID":"00000001","Key":"B","Num":"1","Time":"18:00:27"}]}
        */

        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        public delegate void CallbackDelegate(int msg, int param1, string param2);

        //[DllImport("SWComApi.dll")]
        [DllImport("SWComApi.dll", CallingConvention = CallingConvention.Cdecl)]
        internal static extern int SWCom_SetCallbackAddrWithJsonNoSort(CallbackDelegate pfAddr);  //以JSON格式输出
    }
}
