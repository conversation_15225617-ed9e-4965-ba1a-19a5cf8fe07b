"""
CallTag Controller - Python library for controlling sound-light tag readers
声光标签读卡器Python控制库

This library provides a Python interface to control sound-light tag readers
that can be connected via Serial, USB, TCP Client, or TCP Server connections.

Author: AI Assistant
Version: 1.0.0
"""

from .core.device import CallTagDevice
from .core.tag_info import TagInfo
from .connections.serial_conn import SerialConnection
from .connections.usb_conn import USBConnection
from .connections.tcp_client import TCPClientConnection
from .connections.tcp_server import TCPServerConnection

__version__ = "1.0.0"
__author__ = "AI Assistant"

__all__ = [
    'CallTagDevice',
    'TagInfo',
    'SerialConnection',
    'USBConnection', 
    'TCPClientConnection',
    'TCPServerConnection'
]
