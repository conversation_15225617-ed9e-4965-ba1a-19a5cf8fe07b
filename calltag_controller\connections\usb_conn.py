"""
USB HID connection implementation for CallTag devices
USB HID连接实现
"""

import time
from typing import Optional, List


class USBConnection:
    """
    USB HID connection for CallTag devices
    声光标签设备的USB HID连接
    """
    
    def __init__(self, vendor_id: int = 0x0483, product_id: int = 0x5750):
        """
        Initialize USB HID connection
        
        Args:
            vendor_id (int): USB Vendor ID
            product_id (int): USB Product ID
        """
        self.vendor_id = vendor_id
        self.product_id = product_id
        self.device = None
        self.is_connected = False
        
        # Try to import HID library
        try:
            import hid
            self.hid = hid
        except ImportError:
            self.hid = None
            print("hidapi not installed. Install with: pip install hidapi")
    
    def connect(self, device_index: int = 0) -> bool:
        """
        Connect to USB HID device
        连接USB HID设备
        
        Args:
            device_index (int): Device index if multiple devices found
            
        Returns:
            bool: True if connection successful
        """
        if not self.hid:
            print("HID library not available")
            return False
        
        try:
            # Find devices
            devices = self.get_available_devices()
            if not devices or device_index >= len(devices):
                print(f"No device found at index {device_index}")
                return False
            
            device_info = devices[device_index]
            
            # Open device
            self.device = self.hid.device()
            self.device.open(device_info['vendor_id'], device_info['product_id'])
            
            if self.device:
                self.is_connected = True
                # Set non-blocking mode
                self.device.set_nonblocking(1)
                return True
            
            return False
        except Exception as e:
            print(f"USB connection failed: {e}")
            return False
    
    def disconnect(self):
        """
        Disconnect from USB HID device
        断开USB HID连接
        """
        if self.device:
            try:
                self.device.close()
            except:
                pass
        self.is_connected = False
        self.device = None
    
    def send(self, data: bytes) -> bool:
        """
        Send data through USB HID
        通过USB HID发送数据
        
        Args:
            data (bytes): Data to send
            
        Returns:
            bool: True if sent successfully
        """
        if not self.is_connected or not self.device:
            return False
        
        try:
            # HID reports typically need to be padded to report size
            # Assuming 64-byte reports (common for HID devices)
            report_size = 64
            padded_data = data + b'\x00' * (report_size - len(data))
            
            bytes_written = self.device.write(padded_data[:report_size])
            return bytes_written > 0
        except Exception as e:
            print(f"USB send failed: {e}")
            return False
    
    def receive(self, max_size: int = 64) -> bytes:
        """
        Receive data from USB HID
        从USB HID接收数据
        
        Args:
            max_size (int): Maximum bytes to read
            
        Returns:
            bytes: Received data
        """
        if not self.is_connected or not self.device:
            return b''
        
        try:
            data = self.device.read(max_size)
            if data:
                # Remove padding zeros
                return bytes(data).rstrip(b'\x00')
            return b''
        except Exception as e:
            print(f"USB receive failed: {e}")
            return b''
    
    def get_available_devices(self) -> List[dict]:
        """
        Get list of available USB HID devices
        获取可用的USB HID设备列表
        
        Returns:
            List[dict]: List of device information
        """
        if not self.hid:
            return []
        
        try:
            devices = []
            for device_dict in self.hid.enumerate():
                # Filter by vendor/product ID if specified
                if (self.vendor_id == 0 or device_dict['vendor_id'] == self.vendor_id) and \
                   (self.product_id == 0 or device_dict['product_id'] == self.product_id):
                    devices.append({
                        'vendor_id': device_dict['vendor_id'],
                        'product_id': device_dict['product_id'],
                        'serial_number': device_dict.get('serial_number', ''),
                        'manufacturer_string': device_dict.get('manufacturer_string', ''),
                        'product_string': device_dict.get('product_string', ''),
                        'path': device_dict.get('path', b'').decode('utf-8', errors='ignore')
                    })
            return devices
        except Exception as e:
            print(f"Failed to enumerate USB devices: {e}")
            return []
    
    def get_device_info(self) -> dict:
        """
        Get information about the connected device
        获取已连接设备的信息
        
        Returns:
            dict: Device information
        """
        if not self.is_connected or not self.device:
            return {}
        
        try:
            return {
                'manufacturer': self.device.get_manufacturer_string(),
                'product': self.device.get_product_string(),
                'serial_number': self.device.get_serial_number_string(),
                'vendor_id': self.vendor_id,
                'product_id': self.product_id
            }
        except Exception as e:
            print(f"Failed to get device info: {e}")
            return {}
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.disconnect()
