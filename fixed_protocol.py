#!/usr/bin/env python3
"""
Fixed protocol implementation based on real device responses
基于真实设备响应的修复协议实现
"""

import struct
from typing import List, <PERSON><PERSON>, Optional
from calltag_controller.core.tag_info import TagInfo


class FixedProtocol:
    """
    Fixed protocol implementation for real CallTag devices
    真实CallTag设备的修复协议实现
    """
    
    # Command constants
    CMD_START_READ = 0x41
    CMD_STOP_READ = 0x40
    CMD_INQUIRY = 0x43
    CMD_CALL_TAG = 0x65
    CMD_STOP_CALL_TAG = 0x66
    
    # LED modes
    LED_RED_ONLY = 0x00
    LED_GREEN_ONLY = 0x01
    LED_CROSS_MODE = 0x02
    LED_ALTERNATE_MODE = 0x03
    LED_FLASH_MODE = 0x04
    
    # Time durations
    TIME_10_SEC = 0x00
    TIME_30_SEC = 0x01
    TIME_60_SEC = 0x02
    TIME_5_MIN = 0x03
    TIME_PERMANENT = 0x04
    
    @staticmethod
    def calculate_checksum(data: bytes) -> int:
        """Calculate checksum for the data"""
        checksum = sum(data) & 0xFF
        return ((~checksum) + 1) & 0xFF
    
    @staticmethod
    def create_command_packet(addr: int, cmd: int, params: bytes = b'') -> bytes:
        """Create a command packet"""
        param_len = len(params)
        total_len = param_len + 3  # addr + cmd + checksum
        
        packet = bytearray()
        packet.extend(b'SW')  # Header
        packet.append(0)      # Length high byte
        packet.append(total_len)  # Length low byte
        packet.append(addr)   # Device address
        packet.append(cmd)    # Command
        packet.extend(params) # Parameters
        
        # Calculate and append checksum
        checksum = FixedProtocol.calculate_checksum(packet)
        packet.append(checksum)
        
        return bytes(packet)
    
    @staticmethod
    def create_start_read_command(addr: int = 1) -> bytes:
        """Create start read command"""
        return FixedProtocol.create_command_packet(addr, FixedProtocol.CMD_START_READ)
    
    @staticmethod
    def create_stop_read_command(addr: int = 1) -> bytes:
        """Create stop read command"""
        return FixedProtocol.create_command_packet(addr, FixedProtocol.CMD_STOP_READ)
    
    @staticmethod
    def create_inquiry_command(addr: int = 1) -> bytes:
        """Create inquiry command"""
        return FixedProtocol.create_command_packet(addr, FixedProtocol.CMD_INQUIRY)
    
    @staticmethod
    def create_call_tag_command(addr: int, tag_id: int, led_mode: int, time_mode: int, buzzer_enable: bool = True) -> bytes:
        """Create call tag command"""
        # Convert tag_id to 4 bytes (big endian)
        id_bytes = struct.pack('>I', tag_id)
        
        # Combine LED mode and buzzer enable
        led_state = led_mode
        if buzzer_enable:
            led_state |= 0x10
        
        params = id_bytes + bytes([led_state, time_mode])
        return FixedProtocol.create_command_packet(addr, FixedProtocol.CMD_CALL_TAG, params)
    
    @staticmethod
    def create_stop_call_command(addr: int, tag_id: int) -> bytes:
        """Create stop call command"""
        id_bytes = struct.pack('>I', tag_id)
        params = id_bytes + b'\x00\x00'  # LED state and time set to 0
        return FixedProtocol.create_command_packet(addr, FixedProtocol.CMD_STOP_CALL_TAG, params)
    
    @staticmethod
    def parse_ct_response(data: bytes) -> Tuple[bool, List[TagInfo]]:
        """
        Parse CT response format from real device
        解析真实设备的CT响应格式

        Based on C# code analysis:
        CT [LEN_H] [LEN_L] [ADDR] [CMD] [STATUS] [RESERVED] [RESERVED] [TAG_COUNT] [TAG_DATA...] [CHECKSUM]
        Tag count is at position 8 (data[8])
        Tag data starts at position 9, each tag is 12 bytes
        """
        if len(data) < 9:  # Need at least header + tag count
            return False, []

        # Check header
        if data[:2] != b'CT':
            return False, []

        try:
            # Extract length
            length = (data[2] << 8) | data[3]

            if len(data) < length + 4:  # Header + length + data
                return False, []

            # Extract fields
            addr = data[4]
            cmd = data[5]
            status = data[6] if len(data) > 6 else 0

            # Tag count is at position 8 (based on C# code)
            tag_count = data[8] if len(data) > 8 else 0

            print(f"🔍 CT Response: Addr={addr}, Cmd=0x{cmd:02X}, Status=0x{status:02X}, TagCount={tag_count}")

            # Parse tag data
            tags = []
            if tag_count > 0 and len(data) >= 9:
                # Tag data starts at position 9
                tag_data_start = 9
                tag_data_end = min(tag_data_start + tag_count * 12, len(data) - 1)  # Exclude checksum
                tag_data = data[tag_data_start:tag_data_end]
                tags = FixedProtocol._parse_binary_tag_data(tag_data, tag_count)

            return True, tags

        except Exception as e:
            print(f"❌ Error parsing CT response: {e}")
            return False, []
    
    @staticmethod
    def _parse_binary_tag_data(data: bytes, count: int) -> List[TagInfo]:
        """
        Parse binary tag data based on C# ParseTags function
        基于C# ParseTags函数解析二进制标签数据

        From C# code:
        - Each tag is 12 bytes
        - Tag ID is at bytes 2-5 (skip first 2 bytes 0x0B 0x20)
        - State is at byte 8
        - Format: int tagId = ((int)idata[i * 12 + 2] << 24) | ((int)idata[i * 12 + 3] << 16) | ((int)idata[i * 12 + 4] << 8) | (int)idata[i * 12 + 5];
        """
        tags = []

        try:
            tag_size = 12

            print(f"   Parsing {count} tags from {len(data)} bytes of data")

            for i in range(count):
                offset = i * tag_size
                if offset + tag_size <= len(data):
                    tag_bytes = data[offset:offset + tag_size]

                    print(f"   Tag {i+1} raw bytes: {tag_bytes.hex()}")

                    # Extract tag ID from bytes 2-5 (skip first 2 bytes)
                    # C# code: ((int)idata[i * 12 + 2] << 24) | ((int)idata[i * 12 + 3] << 16) | ((int)idata[i * 12 + 4] << 8) | (int)idata[i * 12 + 5]
                    tag_id = (tag_bytes[2] << 24) | (tag_bytes[3] << 16) | (tag_bytes[4] << 8) | tag_bytes[5]

                    # Extract state from byte 8
                    state = tag_bytes[8] if len(tag_bytes) > 8 else 0

                    print(f"   Tag {i+1}: ID={tag_id}, State=0x{state:02X}")
                    print(f"   First 2 bytes: 0x{tag_bytes[0]:02X} 0x{tag_bytes[1]:02X}")

                    # Check if this matches our known tags
                    if tag_id in [2654, 399]:
                        # Create TagInfo
                        tag_info = TagInfo(
                            tag_id=str(tag_id).zfill(10),  # Pad to 10 digits like C# code
                            state=state,
                            num=1,
                            tag_type="DetectedTag"
                        )
                        tags.append(tag_info)
                        print(f"   ✅ KNOWN tag found: {tag_info}")
                    else:
                        # Still add unknown tags for debugging
                        tag_info = TagInfo(
                            tag_id=str(tag_id).zfill(10),
                            state=state,
                            num=1,
                            tag_type="UnknownTag"
                        )
                        tags.append(tag_info)
                        print(f"   ❓ Unknown tag: {tag_info}")
                else:
                    print(f"   ⚠️  Tag {i+1}: Not enough data (need {tag_size}, have {len(data) - offset})")

        except Exception as e:
            print(f"❌ Error parsing binary tag data: {e}")
            import traceback
            traceback.print_exc()

        return tags
    
    @staticmethod
    def parse_response(data: bytes) -> Tuple[bool, Optional[dict]]:
        """
        Parse response data from device (handles both SW and CT formats)
        解析设备响应数据（处理SW和CT格式）
        """
        if len(data) < 4:
            return False, None
        
        # Check for CT format (real device)
        if data[:2] == b'CT':
            success, tags = FixedProtocol.parse_ct_response(data)
            if success:
                return True, {
                    'format': 'CT',
                    'tags': tags,
                    'raw_data': data
                }
        
        # Check for SW format (our protocol)
        elif data[:2] == b'SW':
            # Original SW parsing logic
            length = (data[2] << 8) | data[3]
            
            if len(data) < length + 4:
                return False, None
            
            # Verify checksum
            packet_data = data[:length + 4 - 1]
            expected_checksum = FixedProtocol.calculate_checksum(packet_data)
            actual_checksum = data[length + 4 - 1]
            
            if expected_checksum != actual_checksum:
                return False, None
            
            addr = data[4]
            cmd = data[5]
            payload = data[6:length + 4 - 1]
            
            return True, {
                'format': 'SW',
                'address': addr,
                'command': cmd,
                'payload': payload
            }
        
        return False, None


def test_fixed_protocol():
    """Test the fixed protocol with real device data"""
    print("Testing Fixed Protocol with Real Device Data")
    print("=" * 50)
    
    # Real captured responses
    test_responses = [
        bytes.fromhex("4354000401410122"),  # Start read response
        bytes.fromhex("4354001e01430100020b2000000a5e0000007800000b200000018f000000500000ee"),  # 2 tags
        bytes.fromhex("4354002a01430100030b2000000a5e0000007800000b200000018f0000005000000b2000000a5e000000780000d6"),  # 3 tags
    ]
    
    for i, response in enumerate(test_responses):
        print(f"\n--- Testing Response {i+1} ---")
        print(f"Raw data: {response.hex()}")
        
        success, result = FixedProtocol.parse_response(response)
        if success:
            print(f"✅ Parsed successfully!")
            if 'tags' in result:
                tags = result['tags']
                print(f"Found {len(tags)} tags:")
                for tag in tags:
                    print(f"  • {tag}")
            else:
                print(f"Result: {result}")
        else:
            print("❌ Failed to parse")


if __name__ == '__main__':
    test_fixed_protocol()
