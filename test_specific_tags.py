#!/usr/bin/env python3
"""
Test script for specific CallTag devices
针对特定标签的测试脚本

Known tags:
- ID: 2654
- ID: 399
"""

import time
import sys
from calltag_controller import CallTagDevice, TCPClientConnection
from calltag_controller.core.protocol import Protocol


class SpecificTagTest:
    """Test class for specific tags"""
    
    def __init__(self):
        self.device_ip = "*************"
        self.device_port = 60000
        self.device = None
        self.known_tags = [2654, 399]  # Known tag IDs
        self.detected_tags = {}
        
    def setup_device(self):
        """Setup device connection"""
        print(f"🔧 Setting up connection to {self.device_ip}:{self.device_port}")
        
        connection = TCPClientConnection(self.device_ip, self.device_port, timeout=10.0)
        self.device = CallTagDevice(connection)
        
        # Set up callbacks
        self.device.on_tag_detected = self.on_tag_detected
        self.device.on_error = self.on_error
        self.device.on_connection_lost = self.on_connection_lost
        
        print("✅ Device setup complete")
    
    def on_tag_detected(self, tag_info):
        """Callback when tag is detected"""
        tag_id = int(tag_info.id) if tag_info.id.isdigit() else tag_info.id
        
        # Check if this is one of our known tags
        is_known = tag_id in self.known_tags or str(tag_id) in [str(t) for t in self.known_tags]
        status = "🎯 KNOWN" if is_known else "❓ UNKNOWN"
        
        print(f"🏷️  Tag detected: ID={tag_info.id} ({tag_id}) {status}, State=0x{tag_info.state:02X}, Count={tag_info.num}")
        
        # Store in our detected tags
        self.detected_tags[tag_info.id] = tag_info
    
    def on_error(self, error_msg):
        """Callback for errors"""
        print(f"❌ Error: {error_msg}")
    
    def on_connection_lost(self):
        """Callback when connection is lost"""
        print("⚠️  Connection lost!")
    
    def connect(self):
        """Connect to device"""
        print("🔌 Connecting to device...")
        if self.device.connect():
            print("✅ Connected successfully!")
            return True
        else:
            print("❌ Failed to connect!")
            return False
    
    def scan_for_tags(self, duration=15):
        """Scan for tags for specified duration with debug info"""
        print(f"\n📡 Scanning for tags (looking for IDs: {self.known_tags})...")
        print(f"⏱️  Scanning for {duration} seconds...")

        # First try manual inquiry to see raw responses
        print("🔍 Testing manual inquiry first...")
        self._test_manual_inquiry()

        if not self.device.start_reading():
            print("❌ Failed to start reading")
            return False

        print("✅ Reading started")

        # Scan for the specified duration
        start_time = time.time()
        last_update = 0

        while time.time() - start_time < duration:
            elapsed = int(time.time() - start_time)
            if elapsed != last_update:
                remaining = duration - elapsed
                print(f"⏱️  {remaining} seconds remaining... (Found {len(self.detected_tags)} tags)", end='\r')
                last_update = elapsed
            time.sleep(0.1)

        print("\n")
        self.device.stop_reading()
        print("⏹️  Stopped reading")

        # Show results
        print(f"\n📊 Scan Results:")
        print(f"   Total tags detected: {len(self.detected_tags)}")

        if self.detected_tags:
            print("   Detected tags:")
            for tag_id, tag_info in self.detected_tags.items():
                numeric_id = int(tag_id) if tag_id.isdigit() else tag_id
                is_known = numeric_id in self.known_tags
                status = "🎯 KNOWN" if is_known else "❓ UNKNOWN"
                print(f"     • ID: {tag_id} {status}, State: 0x{tag_info.state:02X}, Detections: {tag_info.num}")
        else:
            print("   ❌ No tags detected")

        return len(self.detected_tags) > 0

    def _test_manual_inquiry(self):
        """Test manual inquiry to see raw device responses"""
        print("🔧 Testing manual inquiry...")

        from calltag_controller.core.protocol import Protocol

        # Get the raw connection
        connection = self.device.connection

        # Send start read command
        start_cmd = Protocol.create_start_read_command(1)
        print(f"📤 Sending start read: {start_cmd.hex()}")
        connection.send(start_cmd)
        time.sleep(0.5)

        # Check for response
        response = connection.receive()
        if response:
            print(f"📥 Start read response: {response.hex()}")
            try:
                text = response.decode('utf-8', errors='ignore')
                if text.strip():
                    print(f"📄 As text: {text}")
            except:
                pass

        # Send inquiry command multiple times
        for i in range(3):
            inquiry_cmd = Protocol.create_inquiry_command(1)
            print(f"📤 Sending inquiry {i+1}: {inquiry_cmd.hex()}")
            connection.send(inquiry_cmd)
            time.sleep(1)  # Wait longer for response

            response = connection.receive()
            if response:
                print(f"📥 Inquiry response {i+1} ({len(response)} bytes): {response.hex()}")
                try:
                    text = response.decode('utf-8', errors='ignore')
                    if text.strip():
                        print(f"📄 As text: {text}")

                        # Try to parse as JSON
                        if 'AttenceID' in text or '{' in text:
                            print("🔍 Found potential JSON data!")
                            tags = Protocol.parse_tag_data(text)
                            if tags:
                                print(f"✅ Parsed {len(tags)} tags:")
                                for tag in tags:
                                    print(f"   • {tag}")
                            else:
                                print("❌ Failed to parse JSON")
                except Exception as e:
                    print(f"❌ Error parsing response: {e}")
            else:
                print(f"📭 No response to inquiry {i+1}")

        print("🔧 Manual inquiry test complete")
    
    def test_known_tags(self):
        """Test calling the known tags"""
        print(f"\n🔊 Testing Known Tags: {self.known_tags}")
        
        for tag_id in self.known_tags:
            print(f"\n🎯 Testing tag ID: {tag_id}")
            
            # Check if this tag was detected
            tag_id_str = str(tag_id).zfill(10)  # Pad with zeros like the original format
            detected = False
            
            for detected_id in self.detected_tags.keys():
                if int(detected_id) == tag_id or detected_id == tag_id_str:
                    detected = True
                    break
            
            if detected:
                print(f"   ✅ Tag {tag_id} was detected during scan")
            else:
                print(f"   ⚠️  Tag {tag_id} was NOT detected during scan (will still try to call)")
            
            # Test different call modes
            test_modes = [
                ("Flash + Buzzer, 10 sec", Protocol.LED_FLASH_MODE, Protocol.TIME_10_SEC, True),
                ("Alternate + Buzzer, 30 sec", Protocol.LED_ALTERNATE_MODE, Protocol.TIME_30_SEC, True),
            ]
            
            for description, led_mode, time_mode, buzzer in test_modes:
                print(f"   🔄 Testing: {description}")
                
                if self.device.call_tag(tag_id, led_mode, time_mode, buzzer):
                    print(f"   ✅ Call command sent for tag {tag_id}")
                    print(f"   👀 Look for flashing lights and listen for buzzer on tag {tag_id}")
                    
                    # Wait to observe
                    wait_time = 5
                    for i in range(wait_time):
                        print(f"   ⏱️  Observing... {wait_time - i} seconds", end='\r')
                        time.sleep(1)
                    print()
                    
                    # Stop calling
                    if self.device.stop_call_tag(tag_id):
                        print(f"   ⏹️  Stop command sent for tag {tag_id}")
                    else:
                        print(f"   ❌ Failed to send stop command for tag {tag_id}")
                else:
                    print(f"   ❌ Failed to send call command for tag {tag_id}")
                
                time.sleep(2)  # Brief pause between tests
    
    def interactive_test(self):
        """Interactive testing mode"""
        print(f"\n🎮 Interactive Mode")
        print(f"Known tags: {self.known_tags}")
        print("Commands:")
        print("  scan          - Scan for tags")
        print("  call <id>     - Call specific tag")
        print("  stop <id>     - Stop calling tag")
        print("  call 2654     - Call tag 2654")
        print("  call 399      - Call tag 399")
        print("  test          - Test all known tags")
        print("  tags          - Show detected tags")
        print("  quit          - Exit")
        print()
        
        while True:
            try:
                cmd = input("TagTest> ").strip().split()
                
                if not cmd:
                    continue
                
                command = cmd[0].lower()
                
                if command == 'quit':
                    break
                elif command == 'scan':
                    self.scan_for_tags()
                elif command == 'call':
                    if len(cmd) > 1:
                        try:
                            tag_id = int(cmd[1])
                            print(f"🔊 Calling tag {tag_id}...")
                            if self.device.call_tag(tag_id, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True):
                                print(f"✅ Call command sent for tag {tag_id}")
                            else:
                                print(f"❌ Failed to call tag {tag_id}")
                        except ValueError:
                            print("❌ Invalid tag ID")
                    else:
                        print("Usage: call <tag_id>")
                elif command == 'stop':
                    if len(cmd) > 1:
                        try:
                            tag_id = int(cmd[1])
                            print(f"⏹️  Stopping call for tag {tag_id}...")
                            if self.device.stop_call_tag(tag_id):
                                print(f"✅ Stop command sent for tag {tag_id}")
                            else:
                                print(f"❌ Failed to stop calling tag {tag_id}")
                        except ValueError:
                            print("❌ Invalid tag ID")
                    else:
                        print("Usage: stop <tag_id>")
                elif command == 'test':
                    self.test_known_tags()
                elif command == 'tags':
                    if self.detected_tags:
                        print(f"📊 Detected tags ({len(self.detected_tags)}):")
                        for tag_id, tag_info in self.detected_tags.items():
                            numeric_id = int(tag_id) if tag_id.isdigit() else tag_id
                            is_known = numeric_id in self.known_tags
                            status = "🎯" if is_known else "❓"
                            print(f"  {status} ID: {tag_id}, State: 0x{tag_info.state:02X}, Count: {tag_info.num}")
                    else:
                        print("📭 No tags detected yet. Run 'scan' first.")
                else:
                    print(f"❌ Unknown command: {command}")
            
            except EOFError:
                break
            except KeyboardInterrupt:
                print("\nUse 'quit' to exit")
    
    def disconnect(self):
        """Disconnect from device"""
        if self.device:
            self.device.stop_reading()
            self.device.disconnect()
            print("🔌 Disconnected from device")


def main():
    """Main function"""
    print("CallTag Controller - Specific Tags Test")
    print("=" * 40)
    print(f"🎯 Target Tags: 2654, 399")
    print(f"📡 Device: *************:60000")
    print()
    
    test = SpecificTagTest()
    
    try:
        # Setup and connect
        test.setup_device()
        
        if not test.connect():
            print("❌ Cannot proceed without connection")
            return 1
        
        # Choose test mode
        print("Choose test mode:")
        print("1. Quick scan and test")
        print("2. Extended scan (30 seconds)")
        print("3. Test known tags directly")
        print("4. Interactive mode")
        
        choice = input("Enter choice (1-4): ").strip()
        
        if choice == '1':
            print("\n🚀 Quick Test Sequence")
            if test.scan_for_tags(10):  # 10 second scan
                test.test_known_tags()
            else:
                print("⚠️  No tags detected, but will try calling known tags anyway")
                test.test_known_tags()
        
        elif choice == '2':
            print("\n🔍 Extended Scan")
            test.scan_for_tags(30)  # 30 second scan
        
        elif choice == '3':
            print("\n🎯 Direct Test")
            test.test_known_tags()
        
        elif choice == '4':
            test.interactive_test()
        
        else:
            print("❌ Invalid choice")
            return 1
    
    except KeyboardInterrupt:
        print("\n\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    finally:
        test.disconnect()
    
    print("\n✅ Test completed!")
    return 0


if __name__ == '__main__':
    sys.exit(main())
