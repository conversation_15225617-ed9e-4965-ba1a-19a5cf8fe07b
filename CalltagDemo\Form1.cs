﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;

using System.Threading;
using System.IO.Ports;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.Net;
using System.Net.Sockets;
using System.Windows.Forms;
using System.Collections.Concurrent;
using System.Runtime.Serialization.Formatters.Binary;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Office.Interop.Excel;
using System.Runtime.InteropServices;
using Application = Microsoft.Office.Interop.Excel.Application;
//using Microsoft.Office.Interop.Excel;


//using STTech.BytesIO.Core;
//using STTech.BytesIO.Tcp;

namespace CalltagDemo
{
    public partial class Form1 : Form
    {
     
        #region 变量定义
        /// <summary>
        /// 连接方式 0:串口 ,1:USB 2:网口本地 3：网口sever
        /// </summary>
        private int ConnectType = 0;
        int sPort = 0;
        /// <summary>
        /// 是否与设备建立通讯连接
        /// </summary>
        private bool IsConnected = false;

        private bool IsButDownFlag = false;

        /// <summary>
        /// 读取标签线程
        /// </summary>
        //private Thread readTagThread;
        /// <summary>
        /// 是否正在读取标签
        /// </summary>
        private bool IsReadTag = false;
        /// <summary>
        /// 是否开启标签过滤
        /// </summary>
        //private bool IsFilter = false;
        ///// <summary>
        ///// 是否启动自动读卡
        ///// </summary>
        //private bool isAutoRead = false;
        /// <summary>
        /// 取数据的间隔时间
        /// </summary>
        //private int IntervalTime = 100;
        /// <summary>
        /// 读写器地址
        /// </summary>
        //private byte readerAddr = 0xff;

        //private ReaderApi api = new ReaderApi();

        //private Socket mServerSocket;
        /// <summary>
        /// TcpServer是否运行中
        /// </summary>
        private bool isTcpServerRun = false;
        public static bool NetReciveFlag = false;

        /// <summary>
        /// 是否进行心跳包机制
        /// </summary>
        private bool isHeartBeat = false;

        private bool isEn = false;

        //time1 
        public bool InquiryFlag = false;

        // client param;
        string ClientIP = "*************";        // 网段;
        string ClientPort = "60000";
        string FindID = "00";
        //sever param
        //private TcpServer server;
        string s_ip = "127.0.0.1";
        string s_port = "60001";
        private TcpListener listener;
        private CancellationTokenSource cts;
        //客户端对象
        public static TcpClient uTcpclient;
        public byte[] uNetReciveBuf = new byte[1024*10];

        // BUZ_LED 状态字;
        bool buzz_enable = false;
        byte B_L_State = 0;        // 蜂鸣器+LED状态字;
        byte S_time_State = 0;     // 时间选项状态字;

        //设备Device数据结构;
        byte[] uDeviceParam = new byte[64];
        // Device Pram;
        public byte uConnectType = 0;
        public byte FilteTime = 0;
        public byte BuzzerEnable = 0;
        public byte RssiValue = 0;
        public byte PowValue = 0;
        public byte TransMode = 0;
        public byte ReaderAddr = 0;
        //NET Parm
        UInt64 MacInfo = 0;
        public byte[] Lip = new byte[4];
        public int Lport = 0;
        public byte[] Mip = new byte[4];
        public byte[] Gip = new byte[4];  //网关
        public byte[] Sip = new byte[4];
        public int Sport = 0;//远程端口
        public int DHCPCheck = 0;
        public int DNSCheck = 0;
        public int HeartTime;
        public byte[] DNSName = new byte[32];
        public byte[] SSIDName = new byte[32];
        public byte[] WIFIPass = new byte[32];
        public byte[] HttpHead = new byte[80];

        /// <summary>
        /// 总读取的次数
        /// </summary>
        private int totalReadTimes = 0;
        /// <summary>
        /// 过滤的列表，Key:标签Id,Value:标签列表对应的下标
        /// </summary>
        //private Dictionary<string, int> filterTagIdList = new Dictionary<string, int>();
        /// <summary>
        /// 将要输出显示的标签信息队列
        /// </summary>
        //private Queue<List<TagInfo>> OutputQueue = new Queue<List<TagInfo>>();

        // 表示AttenceID的类  
        public class AttenceItem
        {
            public string ID { get; set; }
            public string State { get; set; }
        }

        // 表示顶层数据的类  
        public class AttenceData
        {
            public string DevSN { get; set; }
            public List<AttenceItem> AttenceID { get; set; }
        }
        // 创建sever端询盘数据List
        static List<AttenceItem> attenceItems = new List<AttenceItem> { };
        // 创建顶层对象  
        public AttenceData SeverByteListdata = new AttenceData
        {
            DevSN = "FF24062720B6",
            AttenceID = attenceItems
        };
        #endregion

        #region 控件默认初始化
        private void Init_control(int port)
        {
            //sever            
            SPortBox.Text =s_port ;

            switch (ConnectType)
            {
                case 0:  // UART
                    // 获取串口编号;
                    rdUart.Checked = true;
                    rdUSB.Checked = !rdUart.Checked;
                    rdTCPClient.Checked = !rdUart.Checked;
                    rdTCPSever.Checked = !rdUart.Checked;
                    foreach (string portName in SerialPort.GetPortNames())
                    {
                        // 遍历ComboBox中的所有项
                        for (int i = 0; i < COMBox.Items.Count; i++)
                        {
                            // 获取当前项
                            var item = COMBox.Items[i];

                            // 如果当前项是字符串并且与我们想比对的字符相同
                            //  if (item is char && (char)item == 'c')
                            if (String.Equals(portName, item))
                            {
                                // 选择该项
                                COMBox.SelectedIndex = i;
                                break; // 只选择第一个匹配的项
                            }
                        }
                        if (COMBox.SelectedIndex != COMBox.Items.Count)
                        {
                            //显示扫描到的选项
                            COMBox.SelectedIndex.ToString();

                        }
                    }
                    break;
                case 1:  // USB
                    //获取HID，VID，PID字符串；
                    Scan_USBPort();
                    break;
                case 2:  // TCP Client
                    CIpBox.Text = ClientIP;
                    CportBox.Text = ClientPort;
                    break;
                case 3:  // TCP Sever
                         //设置server的端口号
                    SPortBox.Text = s_port;

                    break;
                default: break;
            }
            FindIdBox.Text = FindID;
            //buz默认选择
            checkBuz.Checked = true;
            // 灯光模式选择;
            string[] led_modestring = new string[5];
            led_modestring[0] = "单独红灯";
            led_modestring[1] = "单独绿灯";
            led_modestring[2] = "交叉模式";
            led_modestring[3] = "交替模式";
            led_modestring[4] = "爆闪模式";
            comboBox2.Items.AddRange(led_modestring);
            comboBox2.Text = led_modestring[4];

            //// 时长;
            string[] time_modestring = new string[5];
            time_modestring[0] = "10秒";
            time_modestring[1] = "30秒";
            time_modestring[2] = "60秒";
            time_modestring[3] = "5分钟";
            time_modestring[4] = "永久";
            comboBox3.Items.AddRange(time_modestring);
            comboBox3.Text = time_modestring[1];
            //listview
            radioDec.Checked = true;
            radioHex.Checked = false;

            //tabControl1 事件
            // 为SelectedIndexChanged事件添加事件处理器
            tabControl1.SelectedIndexChanged += TabControl1_SelectedIndexChanged;

            // 添加事件处理
            IvTagData.MouseClick += new MouseEventHandler(IvTagData_MouseClick);

            //// 创建sever端询盘数据List
            //List<AttenceItem> attenceItems = new List<AttenceItem>;
            //// 创建顶层对象  
            //var SeverByteListdata = new AttenceData
            //{
            //    DevSN = "FF24062720B6",
            //    AttenceID = attenceItems
            //};

        }
        #endregion


        public Form1()
        {
            InitializeComponent();
            this.Load += new EventHandler(Form1_Load);
           
        }

        private void Form1_Load(object sender, EventArgs e)
        {
             Init_control(ConnectType);
        }

        #region  连接端口类型 ConnectType选择
        private void rdUSB_CheckedChanged(object sender, EventArgs e)
        {
            if (rdUSB.Checked)
            {
                rdUart.Checked = !rdUSB.Checked;
                rdTCPClient.Checked = !rdUSB.Checked;
                rdTCPSever.Checked = !rdUSB.Checked;
                ConnectType = 1;

                gUART.Visible = false;
                gTCPClient.Visible = false;
                gTcpSever.Visible = false;
                gUSB.Visible = true;
                ConnectButton.Text = "连接";
            }
            Scan_USBPort();
        }
        private void rdUart_CheckedChanged(object sender, EventArgs e)
        {
            if (rdUart.Checked)
            {
                rdUSB.Checked = !rdUart.Checked;
                rdTCPClient.Checked = !rdUart.Checked;
                rdTCPSever.Checked = !rdUart.Checked;
                ConnectType = 0;
                gTCPClient.Visible = false;
                gTcpSever.Visible = false;
                gUSB.Visible = false;
                gUART.Visible = true;
                ConnectButton.Text = "连接";
            }
        }
        private void rdTCPClient_CheckedChanged(object sender, EventArgs e)
        {
            if (rdTCPClient.Checked)
            {
                rdUSB.Checked = !rdTCPClient.Checked;
                rdUart.Checked = !rdTCPClient.Checked;
                rdTCPSever.Checked = !rdTCPClient.Checked;
                ConnectType = 2;

                gTcpSever.Visible = false;
                gUSB.Visible = false;
                gUART.Visible = false;
                gTCPClient.Visible = true;

                CIpBox.Text = ClientIP;
                CportBox.Text = ClientPort;
                ConnectButton.Text = "连接";
            }
        }
        private void rdTCPSever_CheckedChanged(object sender, EventArgs e)
        {
            if (rdTCPSever.Checked)
            {
                rdUSB.Checked = !rdTCPSever.Checked;
                rdUart.Checked = !rdTCPSever.Checked;
                rdTCPClient.Checked = !rdTCPSever.Checked;
                ConnectType = 3;
                gTCPClient.Visible = false;
                gUSB.Visible = false;
                gUART.Visible = false;
                gTcpSever.Visible = true;
                ConnectButton.Text = "侦听";

                SPortBox.Text = s_port;
            }
        }

        #endregion ，

   

        #region  获取数据操作
        public List<TagInfo> list_IvTag = new List<TagInfo>();
        public List<TagInfo> GetList(string str)
        {
            JObject json = (JObject)JsonConvert.DeserializeObject(str);
            List<string> jsonlist = new List<string>();
            try
            {
                var HeaderTagJson = json["AttenceID"];
                var DevSNJson = json["DevSN"];
                if (HeaderTagJson != null)
                {
                    jsonlist.Add(HeaderTagJson.ToString());
                    jsonlist.Add(DevSNJson.ToString());
                    var jlist = JArray.Parse(jsonlist[0].ToString());
                    List<TagInfo> listanswerkey = new List<TagInfo>();
                    foreach (var item in jlist)
                    {
                        TagInfo ans = new TagInfo();
                        // ID数据
                        uint oneCard = Convert.ToUInt32(item["ID"].ToString(), 16);
                        if (oneCard != 0)
                        {
                            string oneCard_S = oneCard.ToString("D10");
                            ans.ID = oneCard_S.ToString();

                        }
                        //状态
                        ans.State = Convert.ToInt32(item["State"]);

                        ans.Type = jsonlist[1].ToString();
                        listanswerkey.Add(ans);
                    }
                    return listanswerkey;
                }
                else
                {
                    return null;
                }
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region sever端询盘操作
        public void SeverSendOut(byte[] data ,int length)
        {
            try
            {
                jytTcpClients[jytConn.SelectedIndex].GetStream().Write(data, 0, length);
            }
            catch
            {
                MessageBox.Show("发送失败", "错误");
            }
        }

        #endregion

        #region  Sever端询盘后，采集数据转成json格式
        // 将新的AttenceItem添加到列表中的方法  
        //static void AddAttenceItemToList(List<AttenceItem> list, string id, string state)
        //{
        //    AttenceItem newItem = new AttenceItem { ID = id, State = state };
        //    list.Add(newItem);
        //}

        //DataInvJsonList :byte[]数据转换维json数据；
        //public static List<AttenceItem> DataInvJsonList(byte[] buf)
        //{
        //    //List<AttenceItem> sss = null;
        //    byte[] pID = new byte[12];

        //    List<AttenceItem> data1 = new List<AttenceItem> { };

        //    int cnt = (int)(buf[7] * 256) + buf[8];

        //    if (cnt > 0)
        //    {
        //        for (int i = 0; i < cnt; i++)
        //        {
        //            byte ilen = (byte)(buf[9 + i * 12]);
        //            if (ilen == 0x0B)
        //            {
        //                byte[] test = new byte[4];
        //                test[0] = buf[9 + i * 12 + 2];
        //                test[1] = buf[9 + i * 12 + 3];
        //                test[2] = buf[9 + i * 12 + 4];
        //                test[3] = buf[9 + i * 12 + 5];
        //                //获取ID，
        //                UInt32 sid = (UInt32)(buf[9 + i * 12 + 2] << 24) | (UInt32)(buf[9 + i * 12 + 3] << 16) | (UInt32)(buf[9 + i * 12 + 4] << 8) | (UInt32)(buf[9 + i * 12 + 5]);
        //                string vid = sid.ToString("D10");

        //                byte ustate = buf[9 + i * 11 + 7];
        //                string vstate = ustate.ToString("X2");

        //                AddAttenceItemToList(data1, vid, vstate);

        //                // AttenceItem newItem = new AttenceItem { ID = v, State = vv };
        //                // AddAttenceItemToList(attenceItems, ID1, state1);
        //                // data1.Add(newItem);
        //            }
        //        }
        //        return data1;
        //    }

        //    return null;
        //}

       // json数据在listView中解析;
        //public  void  JListChangeView(byte[] buf)
        //{
        //    int num = 0;
        //    // 测试解析
        //    string str = System.Text.Encoding.Default.GetString(buf);
        //    List<TagInfo> list3 = GetList(str);
        //    if (str.Contains("AttenceID"))
        //    {
        //        var idToNumSum = new Dictionary<string, int>();
        //        //// 遍历列表，累加每个ID的Num值  
        //        for (int i = 0; i < list3.Count; i++)
        //        {
        //            if (list_IvTag.Count == 0)
        //            {
        //                list3[i].Num++;
        //                list_IvTag.Add(list3[i]);
        //            }
        //            else
        //            {
        //                num = 0;
        //                int j;
        //                for (j = 0; j < list_IvTag.Count; j++)
        //                {
        //                    if (list_IvTag[j].ID == list3[i].ID)
        //                    {
        //                        list_IvTag[j].Num++;
        //                        //string sss = list_IvTag[j].Num.ToString();
        //                        //textBox1.Text = textBox1.Text + sss + "相同ID\r\n";
        //                    }
        //                    else  //不相同
        //                    {
        //                        num++;
        //                    }
        //                }
        //                if (j == num)
        //                {
        //                    list3[i].Num++;
        //                    list_IvTag.Add(list3[i]);

        //                }
        //            }
        //        }
        //    }
        //}



        #endregion


        #region 刷新listView
        private void UpdateListView(TagInfo info ,int sn)
        {
            ListViewItem item = new ListViewItem(sn.ToString("D")); // 序号
      
            item.SubItems.Add(info.ID); // ID
            item.SubItems.Add("00");  // 状态栏
            item.SubItems.Add(info.State.ToString("X2"));
            item.SubItems.Add("calltag");
            item.SubItems.Add(info.Num.ToString("D"));
            IvTagData.Items.Add(item);
        }

        private void UpdateAllListView(List<TagInfo> uls)
        {
            IvTagData.Items.Clear();
            if (uls.Count > 0 && uls.Count < 65535)
            {
                IvTagData.BeginUpdate();
                for (int i = 0; i < uls.Count; i++)
                {
                    UpdateListView(uls[i],i);
                }
                IvTagData.EndUpdate();
            }
            // 确保最后一个项目可见  
            if (IvTagData.Items.Count > 0)
            {
                IvTagData.EnsureVisible(IvTagData.Items.Count - 1);
            }
        }

        #endregion
        private void InquiryButton_Click(object sender, EventArgs e)
        {
            bool bReturn = false;
            byte[] arrBuffer = new byte[1024 * 4];
            //ushort iNum = 0;
            //int num = 0;

            if (IsConnected)
            {
                switch (ConnectType)    // 接口选择；
                {
                    case 0:   //串口询盘
                        RFID.SWComApi.SWCom_StartRead();
                        Thread.Sleep(100);

                        break;
                    case 1:  // USB HID方式;
                        RFID.SWHidApi.SWHid_StartRead();
                        Thread.Sleep(100);
                         break;
                    case 2:  //  TCP Client
                        RFID.SWNetApi.SWNet_StartRead();
                        Thread.Sleep(100);
                        break;
                    case 3:  // TCPsever ,询盘操作

                        SeverOperation.Star_Read();
                        ShowMessage("指令下发");
                        break;
                    default: break;
                }

                InquiryFlag = true;
                InquiryButton.Enabled = false;
                StopInquiryButton.Enabled = true;
                timer1.Start();       // 开询盘定时器;
                ViewTime.Start();     // 开询盘定时器;

            }
            else
            {
                MesBox.Text = MesBox.Text + "未连接" + "\r\n";
               
            }
        }

        //#endregion

        public static byte[] ObjectToByteArray(Object obj)
        {
            if (obj == null)
                return null;

            BinaryFormatter bf = new BinaryFormatter();
            using (MemoryStream ms = new MemoryStream())
            {
                bf.Serialize(ms, obj);
                return ms.ToArray();
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            bool bReturn = false;
            byte[] arrBuffer = new byte[1024 * 4];
            byte[] idata = new byte[1024 * 2];
            ushort iNum = 0;
            int num = 0;

            if (InquiryFlag == true)
            {
                switch (ConnectType)  //接口选择；
                {
                    // 发询盘指令；
                    case 0:   // COM
                        #region  UART口 询盘
                        if ( RFID.SWComApi.SWCom_GetDevCache(arrBuffer, out iNum) == false )
                        {
                            //textBox1.Text = textBox1.Text + ("操作失败\r\n");
                            return;

                        }
                        // 显示错误;
                        string str = System.Text.Encoding.Default.GetString(arrBuffer);
                        // 测试解析
                        List<TagInfo> list = GetList(str);
                        if (str.Contains("AttenceID"))
                        {
                            var idToNumSum = new Dictionary<string, int>();
                            //// 遍历列表，累加每个ID的Num值  
                            for (int i = 0; i < list.Count; i++)
                            {
                                if (list_IvTag.Count == 0)
                                {
                                    list[i].Num++;
                                    list_IvTag.Add(list[i]);
                                }
                                else
                                {
                                    num = 0;
                                    int j;
                                    for (j = 0; j < list_IvTag.Count; j++)
                                    {
                                        if (list_IvTag[j].ID == list[i].ID)
                                        {
                                            list_IvTag[j].Num++;
                                            //string sss = list_IvTag[j].Num.ToString();
                                            //textBox1.Text = textBox1.Text + sss + "相同ID\r\n";
                                        }
                                        else  //不相同
                                        {
                                            num++;
                                        }
                                    }
                                    if (j == num)
                                    {
                                        list[i].Num++;
                                        list_IvTag.Add(list[i]);

                                    }
                                }
                            }
                        }
                        #endregion
                        break;
                    case 1:  // USB
                        #region  USB口 询盘
                        if (RFID.SWHidApi.SWHid_GetDevCache(arrBuffer, out iNum) == false)
                        {
                            MesBox.Text = MesBox.Text + ("操作失败\r\n");
                            //return;
                            ;
                        }
                        // 显示错误;
                        string str1 = System.Text.Encoding.Default.GetString(arrBuffer);
                        //textBox1.Text = textBox1.Text + str1;
                        // 解析
                        List<TagInfo> list1 = GetList(str1);
                        if (str1.Contains("AttenceID"))
                        {
                            var idToNumSum = new Dictionary<string, int>();
                            //// 遍历列表，累加每个ID的Num值  
                            for (int i = 0; i < list1.Count; i++)
                            {
                                if (list_IvTag.Count == 0)
                                {
                                    list1[i].Num++;
                                    list_IvTag.Add(list1[i]);
                                }
                                else
                                {
                                    num = 0;
                                    int j;
                                    for (j = 0; j < list_IvTag.Count; j++)
                                    {
                                        if (list_IvTag[j].ID == list1[i].ID) // 相同
                                        {
                                            list_IvTag[j].Num++;
                                        }
                                        else  //不相同
                                        {
                                            num++;
                                        }
                                    }
                                    if (j == num)
                                    {
                                        list1[i].Num++;
                                        list_IvTag.Add(list1[i]);
                                    }
                                }
                            }
                        }
                        #endregion
                        break;
                    case 2:  //TCP clieng
                        #region  TPC Client 询盘
                        if (RFID.SWNetApi.SWNet_GetDevCache(arrBuffer, out iNum) == false)
                        {
                            MesBox.Text = MesBox.Text + ("操作失败\r\n");
                            return;
                        }
                        // 显示错误;
                        string str2 = System.Text.Encoding.Default.GetString(arrBuffer);
                        // 解析
                        List<TagInfo> list2 = GetList(str2);
                        if (str2.Contains("AttenceID"))
                        {
                            var idToNumSum = new Dictionary<string, int>();
                            //// 遍历列表，累加每个ID的Num值  
                            for (int i = 0; i < list2.Count; i++)
                            {
                                if (list_IvTag.Count == 0)
                                {
                                    list2[i].Num++;
                                    list_IvTag.Add(list2[i]);
                                }
                                else
                                {
                                    num = 0;
                                    int j;
                                    for (j = 0; j < list_IvTag.Count; j++)
                                    {
                                        if (list_IvTag[j].ID == list2[i].ID) // 相同
                                        {
                                            list_IvTag[j].Num++;
                                        }
                                        else  //不相同
                                        {
                                            num++;
                                        }
                                    }
                                    if (j == num)
                                    {
                                        list2[i].Num++;
                                        list_IvTag.Add(list2[i]);
                                    }
                                }
                            }
                        }
                     #endregion
                        break;
                    case 3:  //TCP sever
                        #region  TCP sever 模式下询盘

                           SeverOperation.Sever_InquiryOperation(1);  //发送获取缓存数据指令;
                           Thread.Sleep(50);

                        #endregion
                        break;
                    default: break;
                }
            }

        }



        private void Clearbutton_Click(object sender, EventArgs e)
        {
            // 清列表 及list缓存
            IvTagData.Items.Clear();
            list_IvTag.Clear();
            TagNumlabel.Text = "00";
        }

        private void StopInquiryButton_Click(object sender, EventArgs e)
        {
            switch (ConnectType)  //接口选择；
            {
                case 0:   //串口询盘
                    RFID.SWComApi.SWCom_StopRead();
                    Thread.Sleep(10);
                    break;
                case 1:   // USB口
                    RFID.SWHidApi.SWHid_StopRead();
                    Thread.Sleep(10);
                    break;
                case 2:    //TCP Client
                    RFID.SWNetApi.SWNet_StopRead();
                    break;
                case 3:    //TCP Sever
                    SeverOperation.Stop_Read();
                    break;

            }
            MesBox.Text = MesBox.Text + ("询盘停止\r\n");
            timer1.Stop();
            ViewTime.Stop();
            InquiryButton.Enabled = true;
        }

        #region 清textbox1
        private void button1_Click(object sender, EventArgs e)
        {
            MesBox.Clear();
        }
        #endregion



        private void IvTagData_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                ListViewHitTestInfo info = IvTagData.HitTest(e.X, e.Y);
                ListViewItem item = info.Item;

                if (item != null)
                {
                    // 处理点击事件
                    MessageBox.Show("你点击了: " + item.Text);
                    ShowMessage("你点击了: ");
                }
            }
        }


        #region 刷新listView
        private void ViewTime_Tick(object sender, EventArgs e)
        {
            byte[] idata = new byte[1024 * 2];
            if (IsConnected)
            {
                UpdateAllListView(list_IvTag);  //刷新listview
                TagNumlabel.Text = list_IvTag.Count.ToString("D");// 刷新总数；
            }
        }
        #endregion




        #region 扫描USB端口,扫描UART；

        private void Scan_UartPort()
        {
            if(sPort==0)
            {
                rdUart.Checked = true;
                rdUSB.Checked = !rdUart.Checked;
                rdTCPClient.Checked = !rdUart.Checked;
                rdTCPSever.Checked = !rdUart.Checked;
                foreach (string portName in SerialPort.GetPortNames())
                {
                    // 遍历ComboBox中的所有项
                    for (int i = 0; i < COMBox.Items.Count; i++)
                    {
                        // 获取当前项
                        var item = COMBox.Items[i];

                        // 如果当前项是字符串并且与我们想比对的字符相同
                        //  if (item is char && (char)item == 'c')
                        if (String.Equals(portName, item))
                        {
                            // 选择该项
                            COMBox.SelectedIndex = i;
                            break; // 只选择第一个匹配的项
                        }
                    }
                    if (COMBox.SelectedIndex != COMBox.Items.Count)
                    {
                        //显示扫描到的选项
                        COMBox.SelectedIndex.ToString();

                    }
                }
            }
        }

            private void Scan_USBPort()
            {
                 string strSN = "";
                 byte[] arrBuffer = new byte[256];
                int iHidNumber = 0;
            //UInt16 iIndex = 0;
            cbUSB.Items.Clear();

                iHidNumber = RFID.SWHidApi.SWHid_GetUsbCount();
                for (UInt16 iIndex = 0; iIndex < iHidNumber; iIndex++)
                {
                    RFID.SWHidApi.SWHid_GetUsbInfo(iIndex, arrBuffer);
                    strSN = System.Text.Encoding.Default.GetString(arrBuffer);
                cbUSB.Items.Add(strSN);
                 }
                if (iHidNumber > 0)
                cbUSB.SelectedIndex = 0;

        }

        private void ScanButton_Click_1(object sender, EventArgs e)
        {
            switch (ConnectType)
            {
                case 0://UART
                     Scan_UartPort();
                    break;
                case 1: //USB
                     Scan_USBPort();
                    break;
                case 2:  
                    break;
                default: break;

            }
        }
   
        #endregion


        #region 呼叫，停止呼叫操作；
        private void Findbutton_Click(object sender, EventArgs e)
        {
            bool bReturn = false;
            byte[] idata = new byte[4];
            // 发送搜索指令;
            int USER_ID;
            string temp1 = FindIdBox.Text;
            USER_ID = (int)Convert.ToUInt32(temp1);
            idata[0] = (byte)(USER_ID >> 24);
            idata[1] = (byte)(USER_ID >> 16);
            idata[2] = (byte)(USER_ID >> 8);
            idata[3] = (byte)(USER_ID >> 0);
            // 先发停止读指令;
            RFID.SWComApi.SWCom_StopRead();
            Thread.Sleep(10);
            //(checkBox1.Checked == true) ? (buzz_enable = true) : (buzz_enable = false);
            if (checkBuz.Checked == true)
                buzz_enable = true;
            else
                buzz_enable = false;
            B_L_State = 0;
            S_time_State = 0;
            //获取LED状态;
            string  s_temp = comboBox2.Text;
            string  s_time = comboBox3.Text;
            switch (s_temp)
            {
                case "单独红灯":
                    B_L_State = 0x00;
                    break;
                case "单独绿灯":
                    B_L_State = 0x01;
                    break;
                case "交叉模式":
                    B_L_State = 0x02;
                    break;
                case "交替模式":
                    B_L_State = 0x03;
                    break;
                case "爆闪模式":
                    B_L_State = 0x04;
                    break;
                default:
                    B_L_State = 0x02;
                    break;
            }

            if (buzz_enable)
            {
                B_L_State = (byte)(B_L_State | 0x10);
            }
            else
            {
                B_L_State = (byte)(B_L_State & 0xEF);
            }
            // 获取时间间隔；
            switch (s_time)
            {
                case "10秒":
                    S_time_State = 0x00;
                    break;
                case "30秒":
                    S_time_State = 0x01;
                    break;
                case "60秒":
                    S_time_State = 0x02;
                    break;
                case "5分钟":
                    S_time_State = 0x03;
                    break;
                case "永久":
                    S_time_State = 0x04;
                    break;
                default:
                    S_time_State = 0x01;
                    break;
            }
            if(IsConnected)
            {
                switch (ConnectType)
                {
                    case 0:
                        RFID.SWComApi.SWCom_FindID(idata, B_L_State, S_time_State);
                        break;
                    case 1:    // USB
                        RFID.SWHidApi.SWHid_FindID(idata, B_L_State, S_time_State);
                        break;
                    case 2:   // NET client
                        RFID.SWNetApi.SWNet_FindID(idata, B_L_State, S_time_State);
                        break;
                    case 3:  // TCP sever; 
                        SeverOperation.Sever_CallOperation( 1, idata, B_L_State, S_time_State);
                        break;
                    default: break;
                }
                MesBox.Text = MesBox.Text + "搜索指令下发\r\n";
            }
            else
            {
                MesBox.Text = MesBox.Text + "请建立连接\r\n";
            }
        }

        private void StopFindbutton_Click(object sender, EventArgs e)
        {
            byte[] idata = new byte[4];
            // 发送搜索指令;
            int USER_ID;
            string temp1;
            temp1 = FindIdBox.Text;
            USER_ID = (int)Convert.ToUInt32(temp1);
            idata[0] = (byte)(USER_ID >> 24);
            idata[1] = (byte)(USER_ID >> 16);
            idata[2] = (byte)(USER_ID >> 8);
            idata[3] = (byte)(USER_ID >> 0);

            if (IsConnected)
            {
                switch (ConnectType)
                {
                    case 0:
                        RFID.SWComApi.SWCom_DisFindID(idata);
                        break;
                    case 1:    //USB
                        RFID.SWHidApi.SWHid_DisFindID(idata);
                        break;
                    case 2:   // NET Client
                        RFID.SWNetApi.SWNet_DisFindID(idata);
                        break;
                    case 3:  // Sever
                        SeverOperation.Sever_StopInquiryOperation(1, idata, B_L_State, S_time_State);
                        break; 
                    default: break;
                }
            }
        }



        #endregion

        #region sever端连接事件
        // TCP服务端监听
        TcpListener tcpsever = null;
        /// 客户端连接初始化
        public static List<string> dictInfo = new List<string>();
        //连接的客户端列表
        public static List<TcpClient> jytTcpClients = new List<TcpClient>();
        //连接的客户端监听列表
        public static List<Thread> jytTcpClientThreads = new List<Thread>();

        // TCP 接收数据放置在队列缓存中;
        ConcurrentQueue<byte[]> dataQueue = new ConcurrentQueue<byte[]>();

  

        private void Acceptor(IAsyncResult o)
        {
            TcpListener server = o.AsyncState as TcpListener;
            try
            {
                // 初始化连接的客户端；
                TcpClient jytTcpClient = server.EndAcceptTcpClient(o);
                jytTcpClients.Add(jytTcpClient);    // 获取用户的监听 socket；
                uTcpclient = jytTcpClient;

                dictInfo.Add(((IPEndPoint)jytTcpClient.Client.LocalEndPoint).Port + ":" + jytTcpClient.Client.RemoteEndPoint.ToString());
                server.BeginAcceptTcpClient(new AsyncCallback(Acceptor), server); //继续监听客户端连接

                //连接信息显示;
                jytConn.Invoke(new MethodInvoker(delegate
                {
                    jytConn.DataSource = null;
                    jytConn.DataSource = dictInfo;
                    jytConn.DisplayMember = "Name";
                }));

                // 监听到连接请求，建立连接；
                string str = "\r\nClient已连接\r\n";
                ShowMessage(str);
                IsConnected = true;

                this.BeginInvoke(new MethodInvoker(delegate
                {
                      ConnectButton.Text = "断开";
                }));

                // 开接收线程;
                Thread th = new Thread(Recive);
                jytTcpClientThreads.Add(th);
                th.IsBackground = true;
                th.Start(jytTcpClient);
            }
            catch (ObjectDisposedException ex)
            { //监听被关闭
            }
            catch (Exception ex)
            {
              MessageBox.Show(ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error); 

            }
        }
   
     void Recive(object client)
     {
        NetworkStream reciveStream = ((TcpClient)client).GetStream();
        // Socket socketSend = o as Socket;
        while (true)
        {
            try
            {
                NetworkStream nss = ((TcpClient)client).GetStream();        // 获取到的;
                byte[] data2 = new byte[1024 * 1024];                       // 存储客户端发送过来的数据;
                int _length = reciveStream.Read(data2, 0, data2.Length);
                    if (_length > 0)
                    {
                        string msg = Encoding.Default.GetString(data2, 0, _length);

                        // int length = socketSend.Receive(data2);
                        // string message2 = Encoding.UTF8.GetString(data2, 0, length);
                        //JytAddData(("从ip：" + ((TcpClient)client).Client.RemoteEndPoint.ToString() + "收数据："), msg);

                        if (_length > 10 &&(data2[5]==0x43))
                        {
                            dataQueue.Enqueue(data2);
                            ShowMessage("加队列数据2\r\n");
                            // 接收数据 ,数据在 StartDataProcessing() 线程中处理; 
                        }
                        // 解析数据;
                    }
                    else
                    {
                        try
                        {
                            if (((TcpClient)client) != null && ((TcpClient)client).Connected)
                            {
                                NetworkStream ns = ((TcpClient)client).GetStream();
                                ns.Close();
                                ((TcpClient)client).Close();
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show(ex.Message, "错误");

                        }
                    }
            }
            catch
            { }
         }
       }

        // 这个方法启动一个后台线程来处理队列中的数据  
        // 获取数据报文，解析；
        public static List<TagInfo> ParseTags(byte[] udata)
        {
            var IvTag = new List<TagInfo>();

            // 跳过前8个字节（包括标签数量）  
            int index = 8;
            int tagCount = udata[8]; // 假设标签数量存储在udata[7]（注意：通常索引从0开始）  

            byte[] idata = new byte[tagCount * 12];
            // Array.Copy(udata[9], idata, idata.Length);
            for (int ii = 0; ii < tagCount * 12; ii++)
            {
                idata[ii] = udata[ii + 9];
            }

            for (int i = 0; i < tagCount; i++)
            {
                // 读取Tag ID（4个字节，大端模式）  
                // int tagId = BitConverter.ToInt32(idata, index +2); // 跳过前面的0x0B和0x20  
                int tagId = ((int)idata[i * 12 + 2] << 24) | ((int)idata[i * 12 + 3] << 16) | ((int)idata[i * 12 + 4] << 8) | (int)idata[i * 12 + 5];

                // 读取State（1个字节）  
                int state = idata[i * 12 + 8];

                // 创建TagInfo实例并添加到列表  
                var tagInfo = new TagInfo
                {
                    //ID = tagId.ToString("X8"),    // 转换为十六进制字符串;  
                    ID = tagId.ToString("D10"),
                    State = state
                };

                IvTag.Add(tagInfo);

                // 移动到下一个标签的起始位置  
                index += 12;
            }

            return IvTag;
        }

        public void StartDataProcessing()  
        {  
            Task.Run(() =>  
            {  
                while (true)  
                {  
                   if (dataQueue.TryDequeue(out byte[] idata))  
                   {
                        // 处理数据  ,数据直接解析存入 public List<TagInfo> list_IvTag = new List<TagInfo>();
                        //ProcessData(data);  
                        var _IvTag = ParseTags(idata);
                        // 遍历list_IvTag中的每个元素  
                        foreach (var newTag in _IvTag)
                        {
                            // 查找List_OldTag中是否存在具有相同ID的标签  
                            var existingTag = list_IvTag.FirstOrDefault(t => t.ID == newTag.ID);

                            if (existingTag != null)
                            {
                                // 如果存在，则累加Num  
                                existingTag.Num += 1;

                                // 如果不需要进一步处理newTag的其他属性（比如State），则可以跳过将其添加到List_OldTag  
                                // 因为我们只是想更新已存在的项  
                                //continue; // 跳过当前迭代，继续下一个newTag  
                            }
                            else
                            {
                                // 如果不存在具有相同ID的标签，则将newTag添加到List_OldTag  
                                list_IvTag.Add(newTag);
                            }

                        }
                    }
                    else  
                   {  
                       // 如果没有数据，可以暂停一下以避免忙等待  
                        Thread.Sleep(100); // 100毫秒，你可以根据需要调整这个值  
                   }  
               }  
           });  
       }  
    //把数据信息转成 选定的编码格式数据 再 调用添加数据 方法
      public void JytAddData(string ClientIPtxt, string content)
      {
          this.BeginInvoke(new MethodInvoker(delegate
           {
            MesBox.AppendText(ClientIPtxt + content);

            }));
      }

        ////客户端对象
        //static TcpClient tcpclient;
        public static void JytSendOut(byte[] _jytbyte ,int length)
        {

            try
            {
                uTcpclient.GetStream().Write(_jytbyte, 0, length);
            }
            catch
            {
                MessageBox.Show("发送失败", "错误");
            }
        }

        #endregion


        #region 连接操作
        private void ConnectButton_Click(object sender, EventArgs e)
        {
            byte[] arrBuffer = new byte[256];
            bool bReturn = false;
            byte length = 32;
            string str = "", str1 = "";
            byte bDevType;

            if (!IsButDownFlag)      // 连接;
            {
                if(!IsConnected)
                {
                    IsButDownFlag = true;
                    switch (ConnectType)
                    {
                        case 0:  // uart
                            #region uart连接
                            String strPort = COMBox.Text;
                            if (RFID.SWComApi.SWCom_OpenDevice(strPort, 115200))
                            {
                                str= "打开串口成功\r\n";
                                ShowMessage(str);
                                RFID.SWComApi.SWCom_StopRead();
                                Thread.Sleep(10);

                                // 获取参数调用; 获取系统参数;
                                if (RFID.SWComApi.SWCom_GetDeviceSystemInfo(arrBuffer) == false)  // 获取系统信息失败;
                                {
                                    str = "设备不存在!";
                                    ShowMessage(str);
                                    // 修改上传接口，及复位；
                                    return;
                                }
                                else
                                {
                                    str = "参数读取成功!";
                                    ShowMessage(str);
                                    //获取device信息；
                                    RFID.SWComApi.SWCom_ReadDeviceParam(out bDevType, uDeviceParam, 64);
                
                                    string str11 = "", str2 = "", str3 = "";
                                    for (int i = 0; i < 64; i++)
                                    {
                                        str11 = String.Format("{0:X2} ", uDeviceParam[i]);
                                        str2 = str2 + str11;
                                    }
                                    str = "\r\n读取参数成功\r\n";
                                    ShowMessage(str);
                                    IsConnected = true;
                                    ConnectButton.Text = "断开";
                                }
                          }
                          else
                          {
                                str = "操作失败\r\n";
                                ShowMessage(str);
                                return;
                          }
                          #endregion

                            break;
                        case 1:   //usb
                          #region  USB连接
                            if (RFID.SWHidApi.SWHid_OpenDevice((UInt16)cbUSB.SelectedIndex))
                            {
                                MesBox.Text = MesBox.Text + "打开USB成功\r\n";

                                if (RFID.SWHidApi.SWHid_GetDeviceSystemInfo(arrBuffer) == false)     //获取系统信息失败;
                                {
                                    MesBox.Text = MesBox.Text + ("设备不存在!");
                                    RFID.SWHidApi.SWHid_CloseDevice();
                                    return;
                                }
                                else
                                {
                                    MesBox.Text = MesBox.Text + ("连接设备成功!");

                                    if (RFID.SWHidApi.SWHid_ReadDeviceParam(out bDevType, uDeviceParam, 64))
                                    {
                                        MesBox.Text = MesBox.Text + ("Device读取成功!");
                                    }
                                    IsConnected = true;
                                    ConnectButton.Text = "断开";
                                }
                            }
                            else
                            {
                                MesBox.Text = MesBox.Text + "操作失败\r\n";
                                return;
                            }
                            // 获取的数据 第一个字节为 固件版本， 第二个字节为硬件版本， 第3个-第9个为产品序列号
                            // string str = "", str1 = "";
                            str = String.Format("软件版本:{0:D}.{0:D}\r\n", arrBuffer[0] >> 4, uDeviceParam[0] & 0x0F);
                            MesBox.Text = MesBox.Text + str;
                            str = String.Format("硬件版本:{0:D}.{0:D}\r\n", arrBuffer[1] >> 4, uDeviceParam[1] & 0x0F);
                            MesBox.Text = MesBox.Text + str;
                            str = "序列号:";
                            for (int i = 0; i < 7; i++)
                            {
                                str1 = String.Format("{0:X2}", uDeviceParam[2 + i]);
                                str = str + str1;
                            }
                            str = str + "\r\n";
                            MesBox.Text = MesBox.Text + str;

                            Thread.Sleep(100);
                            RFID.SWComApi.SWCom_StopRead();
                            Thread.Sleep(10);

                            #endregion

                            break;
                        case 2:   //tcp client
                            #region  client连接
                            string stringIP = CIpBox.Text;
                            UInt16 iPort = Convert.ToUInt16(CportBox.Text);
                             if (RFID.SWNetApi.SWNet_OpenDevice(stringIP, iPort))
                            {
                                str= "网络设备打开成功\r\n";
                                ShowMessage(str);
                                if (RFID.SWNetApi.SWNet_GetDeviceSystemInfo(arrBuffer) == false)   // 获取系统信息失败;
                                {
                                    str = "设备不存在!";
                                    ShowMessage(str);
                                    RFID.SWNetApi.SWNet_CloseDevice();
                                    return;
                                }
                                else
                                {
                                    // 重新配置一下Device参数;
                                    byte type_temp = 0;
                                    //读取参数 然后根据情况设置  客户也可以不设置参数
                                    if (RFID.SWNetApi.SWNet_ReadDeviceParam(out type_temp, uDeviceParam, 64) == true)
                                    {
                                        //ucBuffer[1]为上传接口  0:USB 1:TCP 2:WIFI 3:RS232 4:TCP JSON 5:RS485 6:GPRS 7:Bluetooth
                                        uDeviceParam[1] = 1;    // 设置成TCP;
                                                                // ucBuffer[6]为上传方式  0:命令方式  1:自动方式 
                                        uDeviceParam[6] = 1;
                                        str = "连接设备成功!";
                                        ShowMessage(str);
                                        IsConnected = true;
                                        ConnectButton.Text = "断开"; 
                                    }
                                    else
                                    {
                                        str ="读取device失败!\r\n";
                                        ShowMessage(str);
                                        return;
                                    }

                                    Thread.Sleep(100);
                                    RFID.SWNetApi.SWNet_StopRead();

                                }
                            }
                            #endregion
                            break;
                        case 3:   //tcp sever 侦听；
                            try
                            {
                                int listenerPort = Convert.ToInt32(SPortBox.Text.Trim());
                                tcpsever = new TcpListener(IPAddress.Any, (int)listenerPort);
                                //启动监听;
                                tcpsever.Start();
                                // 等待连接; 开Client阻塞
                                tcpsever.BeginAcceptTcpClient(new AsyncCallback(Acceptor), tcpsever);
                                // 开监听
                                ShowMessage("开监听");

                                // 启动后台线程来处理队列中的数据  
                                Thread thread = new Thread(new ThreadStart(StartDataProcessing));
                                thread.Start();
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show(ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                            break;
                        default:break;
                    }
                    ShowDeviceParam(uDeviceParam);
                }

            }
            else  // 断开连接
            {
                if (IsConnected)
                {
                    IsButDownFlag = false;
                    switch (ConnectType)
                    {
                        case 0:    // UART
                            RFID.SWComApi.SWCom_StopRead();
                            RFID.SWComApi.SWCom_ReleaseCallBack();
                            RFID.SWComApi.SWCom_CloseDevice();
                            Thread.Sleep(10);
                            IsConnected = false;
                            ConnectButton.Text = "连接";
                            timer1.Stop();
                            //InquiryButton.Enabled = true;
                            str = "关闭串口设备成功\r\n";
                            ShowMessage(str);
                            break;
                        case 1:   // USB
                            RFID.SWHidApi.SWHid_StopRead();
                            RFID.SWHidApi.SWHid_ReleaseCallBack();
                            RFID.SWHidApi.SWHid_CloseDevice();
                            IsConnected = false;
                            ConnectButton.Text = "连接";
                            str = "关闭USB设备成功\r\n";
                            ShowMessage(str);
                            break;
                        case 2:   // client
                            RFID.SWNetApi.SWNet_StopRead();
                            RFID.SWNetApi.SWNet_ReleaseCallBack();
                            RFID.SWNetApi.SWNet_CloseDevice();
                            IsConnected = false;
                            ConnectButton.Text = "连接";
                            str = "关闭网络设备成功\r\n";
                            ShowMessage(str);
                            break;
                        case 3:  //  Client sever
                            //关闭监听
                            tcpsever.Stop();

                            str = "已断开!";
                            ShowMessage(str);
                            IsConnected = false;
                            ConnectButton.Text = "侦听";

                            //循环关闭客户端列表监听
                            foreach (Thread itemthread in jytTcpClientThreads)
                            {
                                itemthread.Abort();
                            }
                            try
                            {

                                //jytConn.ClearSelected();
                                //jytConn.Invoke(new MethodInvoker(delegate
                                //{
                                //    jytConn.DataSource = null;
                                //    jytConn.DataSource = dictInfo;
                                //    jytConn.DisplayMember = "Name";
                                //}));

                                //if (thread != null)
                                //{
                                //    thread.Abort();
                                //    jytAutoSend.Text = "循环发送";
                                //}
                                ShowMessage("信息2");
                            }
                            catch
                            {

                            }
                            break;
                        default:break;
                    }
                }
            }
        }
        #endregion

        #region 信息显示

        public void ShowMessage(string str)
        {
            this.BeginInvoke(new MethodInvoker(delegate
            {
                MesBox.AppendText(str);

            }));
        }

        #endregion

        #region 获取device参数  ,uDeviceParam   设置device参数
        public void  ShowDeviceParam(byte[] Buffer)
        {
            // 获取参数;
            if (IsConnected)
            {
                // 刷新DeviceParam;
                uConnectType = Buffer[1];  //接口
                FilteTime = Buffer[2];    //过滤时间
                BuzzerEnable = Buffer[3];  //蜂鸣器
                RssiValue = Buffer[46];   //RSSI
                PowValue = Buffer[4];     //功率值
                TransMode = Buffer[6];    // 读卡模式
                ReaderAddr = Buffer[7];   //读卡器地址
                switch (uConnectType)
                {
                    case 0:
                        cbPortMode.SelectedIndex = 0;
                        break;
                    case 1:
                        cbPortMode.SelectedIndex = 1;
                        break;
                    case 2:
                        cbPortMode.SelectedIndex = 2;
                        break;
                    case 3:
                        cbPortMode.SelectedIndex = 3;
                        break;
                    case 4:
                        cbPortMode.SelectedIndex = 4;
                        break;
                    case 5:
                        cbPortMode.SelectedIndex = 5;
                        break;
                    case 6:
                        cbPortMode.SelectedIndex = 6;
                        break;
                    case 7:
                        cbPortMode.SelectedIndex = 7;
                        break;
                };
                //过滤时间
                string str3 = String.Format("{0:D2} ", FilteTime);    //过滤时间;
                tbFilterTime.Text = str3;
                //蜂鸣器
                if (BuzzerEnable !=0)
                {
                    cbBuz.Checked = true;
                }
                else
                {
                    cbBuz.Checked = false;
                }
                // RSSI ,textBox3;
                //str3 = String.Format("{0:D2} ", RssiValue);   // 射频功率;
                //tbRssi.Text = str3;

                //textBox5 ,射频功率;
                str3 = String.Format("{0:D2} ", PowValue);   // 射频功率;
                tbPower.Text = str3;

                //textBox6   传输模式
                str3 = String.Format("{0:D2} ", TransMode);   // 读卡模式;
                tbTransMode.Text = str3;

                //textBox7  设备地址
                str3 = String.Format("{0:X2} ", ReaderAddr);   // 设备地址;
                tbAddr.Text = str3;

            }
            else
            {
                ShowMessage("请建立设备连接\r\n");
            }
        }
        public void SetDeviceParam(byte[] Buffer)
        {
            byte bDevType=0;
            if (IsConnected)
            {
                switch (ConnectType)
                {
                    case 0:
                        RFID.SWComApi.SWCom_SetDeviceParam(bDevType, Buffer, 64);
               
                        break;
                    case 1:
                        RFID.SWHidApi.SWHid_SetDeviceParam(bDevType, Buffer, 64);

                        break;
                    case 2:
                        RFID.SWNetApi.SWNet_SetDeviceParam(bDevType, Buffer, 64);

                        break;
                }
                ShowMessage("操作成功\r\n");

            }
        }

        #endregion
        private void TabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            byte[] arrBuffer = new byte[64];
            byte[] arrBuffer1 = new byte[64];
            //bool bReturn = false;
            //byte length = 32;
            string str = "", str1 = "";
            byte bDevType;
            // 获取当前选中的TabPage
            TabPage selectedTab = tabControl1.SelectedTab;

            // 根据选中的TabPage执行不同的逻辑
            switch (selectedTab.Name)
            {
                case "tabPage1":
                    // 加载TabPage1的内容
                    // ShowMessage("page1");
                    break;
                case "tabPage2":
                    // 加载TabPage2的内容
                    // ShowMessage("page2");
                    //先停止读,再获取device参数和net参数;

                    // 获取参数;
                    if(IsConnected)  
                    {
                          switch (ConnectType)    // 接口选择；
                          {
                            case 0:   //串口询盘
                                RFID.SWComApi.SWCom_StopRead();
                                Thread.Sleep(10);
                                break;
                            case 1:  // USB HID方式;
                                RFID.SWHidApi.SWHid_StopRead();
                                Thread.Sleep(10);
                                break;
                            case 2:  //  TCP Client
                                RFID.SWNetApi.SWNet_StopRead();
                                Thread.Sleep(10);
                                break;
                            //case 3:  // TCPsever ,询盘操作
                            //    SeverOperation sOP = new SeverOperation();
                            //    sOP.Sever_InquiryOperation(sOP.StarReadCmd, sOP.sAddr);
                            //    ShowMessage("指令下发");
                            default:break;
                           }

                        //MesBox.Text = MesBox.Text + ("询盘停止\r\n");
                        timer1.Stop();
                        ViewTime.Stop();
                        InquiryButton.Enabled = true;
                        //获取device param
                        ShowDeviceParam(uDeviceParam);
                       }
                    break;
                default:
                    // 默认的处理逻辑
                    ShowMessage("page no");
                    break;
            }
        }

        #region  设备参数获取操作
        private void bReadDevice_Click(object sender, EventArgs e)
        {  //读取系统参数
            byte[] arrBuffer = new byte[64];
            byte bDevType;
            switch (ConnectType)
            {
                case 0:
                    if (RFID.SWComApi.SWCom_ReadDeviceParam(out bDevType, arrBuffer, 64) == false)
                    {
                        ShowMessage("操作失败1\r\n");
                        label22.Text = "读操作失败";
                        return;
                    }
                    break;
                case 1:
                    if (RFID.SWHidApi.SWHid_ReadDeviceParam(out bDevType, arrBuffer, 64) == false)
                    {
                        ShowMessage("操作失败2\r\n");
                        label22.Text = "读操作失败";
                        return;
                    }
                    break;
                case 2:
                    if (RFID.SWNetApi.SWNet_ReadDeviceParam(out bDevType, arrBuffer, 64) == false)
                    {
                        ShowMessage("操作失败3\r\n");
                        label22.Text = "读操作失败";
                        return;
                    }
                    break;
                default:break;
            }
            string str1 = "", str2 = "", str3 = "";
            for (int i = 0; i < 47; i++)
            {
                str1 = String.Format("{0:X2} ", arrBuffer[i]);
                str2 = str2 + str1;
            }

            ShowMessage("\r\n读取参数成功\r\n");
            label22.Text = "读取成功";
            //获取device param
            ShowDeviceParam(uDeviceParam);
        }

        private void bSetDevice_Click(object sender, EventArgs e)
        {
            //byte[] arrBuffer = new byte[64];
            uDeviceParam[0] = 0;    // 网页语言;
                                 //获取控件值;
                                 // 读数据; GetDeviceParamData
            string str;
            //接口
            uConnectType = Convert.ToByte(cbPortMode.SelectedIndex);
            if (ConnectType > 7)
            {
                ShowMessage("输入值错误"); return;
            }
            uDeviceParam[1] = uConnectType;

            //过滤时间
            str = tbFilterTime.Text;
            FilteTime = Convert.ToByte(str);
            if (FilteTime < 250)
            {
                uDeviceParam[2] = FilteTime;
            }
            else
            {
                ShowMessage("过滤时间输入值错误"); return;
            }
            //蜂鸣器开关 BuzzerEnable; 
            if (cbBuz.Checked)
            { BuzzerEnable = 1; }
            else
            { BuzzerEnable = 0; }
            uDeviceParam[3] = BuzzerEnable;

            //射频功率; textBox5  0~31;
            str = tbPower.Text;
            PowValue = Convert.ToByte(str);
            if (PowValue > 31)
            {
                ShowMessage("超过发射功率范围");
                PowValue = 31;
            }
            uDeviceParam[4] = PowValue;

            //RSSI
            //str = tbRssi.Text;
            //RssiValue = Convert.ToByte(str); uDeviceParam[46] = RssiValue;
            //if (RssiValue > 250)
            //{
            //    MessageBox.Show("RSSI输入入值错误"); return;
            //}
            //else
            //    uDeviceParam[46] = RssiValue;

            // mode;
            str = tbTransMode.Text;
            TransMode = Convert.ToByte(str);
            if (TransMode > 1)
            {
                ShowMessage("模式值为0和1"); return;
            }
            else
                uDeviceParam[6] = TransMode;

            //Addr ReaderAddr
            str = tbAddr.Text;
            ReaderAddr = Convert.ToByte(str);
            if (ReaderAddr > 250)
            {
                ShowMessage("地址值输入错误"); return;
            }
            uDeviceParam[7] = ReaderAddr;

            SetDeviceParam(uDeviceParam);

        }

        // 网络参数 --- 读取
        private void bReadNetParam_Click(object sender, EventArgs e)
        {
            byte m_bDevType = 0;  //设备类型
            byte[] arrBuffer = new byte[220];
            if (!IsConnected) return;

            switch (ConnectType)
            {
                case 0:
                    if (RFID.SWComApi.SWCom_ReadDeviceNetParam(out m_bDevType, arrBuffer, 220) == false)
                    {
                        //textBox1.Text = textBox1.Text + ("操作失败\r\n");
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
                case 1:
                    if (RFID.SWHidApi.SWHid_ReadDeviceNetParam(out m_bDevType, arrBuffer, 220) == false)
                    {
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
                case 2:
                    if (RFID.SWNetApi.SWNet_ReadDeviceNetParam(out m_bDevType, arrBuffer, 220) == false)
                    {
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
                case 3:
                    ShowMessage("此模式不支持操作\r\n");
                    break;
                default:break;
            }
            string str1 = "", str2 = "";
            for (int i = 0; i < 220; i++)
            {
                str1 = String.Format("{0:X2} ", arrBuffer[i]);
                str2 = str2 + str1;
            }

            ShowMessage("读取参数成功\r\n");

            // NET Parm显示在设置栏中
            // MAC
            // string temp = Convert.ToString(MacInfo, 16);
            MacInfo = ((UInt64)arrBuffer[0] << 40) + ((UInt64)arrBuffer[1] << 32) + ((UInt64)arrBuffer[2] << 24) + ((UInt64)arrBuffer[3] << 16) +
                       ((UInt64)arrBuffer[4] << 8) + ((UInt64)arrBuffer[5]);
            MacBox.Text = MacInfo.ToString("X12");
            //LIP
            //string str3="";
            for (int i = 0; i < 4; i++)
            {
                Lip[i] = arrBuffer[6 + i];
            }
            IPBox.Text = String.Format("{0:D} ", Lip[0]) + "." + String.Format("{0:D} ", Lip[1]) + "."
                         + String.Format("{0:D} ", Lip[2]) + "." + String.Format("{0:D} ", Lip[3]);
            //Mlp
            for (int i = 0; i < 4; i++)
            {
                Mip[i] = arrBuffer[10 + i];
            }
            MaskBox.Text = String.Format("{0:D} ", Mip[0]) + "." + String.Format("{0:D} ", Mip[1]) + "."
                         + String.Format("{0:D} ", Mip[2]) + "." + String.Format("{0:D} ", Mip[3]);
            //Gip
            for (int i = 0; i < 4; i++)
            {
                Gip[i] = arrBuffer[14 + i];
            }
            GIPBox.Text = String.Format("{0:D} ", Gip[0]) + "." + String.Format("{0:D} ", Gip[1]) + "."
             + String.Format("{0:D} ", Gip[2]) + "." + String.Format("{0:D} ", Gip[3]);

            //Sip
            for (int i = 0; i < 4; i++)
            {
                Sip[i] = arrBuffer[18 + i];
            }
            SeverIPBox.Text = String.Format("{0:D} ", Sip[0]) + "." + String.Format("{0:D} ", Sip[1]) + "."
            + String.Format("{0:D} ", Sip[2]) + "." + String.Format("{0:D} ", Sip[3]);

            //LPORT
            Lport = ((int)arrBuffer[23] << 8) + arrBuffer[22];
            LocalPortBox.Text = Lport.ToString("D");

            //SPORT
            Sport = ((int)arrBuffer[25] << 8) + arrBuffer[24];
            SeverPortBox.Text = Sport.ToString("D");

            //DHCP Enable   //26
            DHCPCheck = arrBuffer[26];
            if (DHCPCheck != 0) DHCPcheckBox.Checked = true;
            else DHCPcheckBox.Checked = false;

            //DNS Enable    //27
            DNSCheck = arrBuffer[27];
            if (DNSCheck != 0) DNScheckBox.Checked = true;
            else DNScheckBox.Checked = false;

            //HeartTime     //28
            HeartTime = arrBuffer[28];
            TickBox.Text = HeartTime.ToString("D");
            //DnsName       //29~29+32 ,93
            for (int i = 0; i < 32; i++)
            {
                DNSName[i] = arrBuffer[29 + i];
                DNSBox.Text = DNSBox.Text + DNSName[i].ToString();
            }
            //WIFIpass       //94 ~126
            for (int i = 0; i < 32; i++)
            {
                WIFIPass[i] = arrBuffer[94 + i];
                WIFIBox.Text = WIFIBox.Text + WIFIPass[i].ToString();
            }
            //HTTP POST      //127~207
            //for (int i = 0; i < 80; i++)
            //{
            //    HttpHead[i] = arrBuffer[127 + i];
            //    HTTPPOSTBox.Text = HTTPPOSTBox.Text + HttpHead[i].ToString();
            //}
        }


        //获取输入栏box中的网络数据
        private void Get_Show_NetParmData(byte[] buf)
        {
            string strTemp;
            UInt16 Atemp = 0;
            //MAC
            strTemp = MacBox.Text;
            MacInfo = Convert.ToUInt64(strTemp, 16);  //16进制字符串转换为长整型

            buf[0] = (byte)(MacInfo >> 40);
            buf[1] = (byte)(MacInfo >> 32);
            buf[2] = (byte)(MacInfo >> 24);
            buf[3] = (byte)(MacInfo >> 16);
            buf[4] = (byte)(MacInfo >> 8);
            buf[5] = (byte)MacInfo;

            //LIP
            strTemp = IPBox.Text;
            string[] arrayIP = strTemp.Split('.');
            for (int i = 0; i < 4; i++)
            {
                Lip[i] = byte.Parse(arrayIP[i]);
                Atemp = UInt16.Parse(arrayIP[i]);
                if (Atemp > 255)
                {
                    MessageBox.Show("IP输入错误"); return;
                }
                buf[6 + i] = Lip[i];
            }
            //Mask  MaskBox
            strTemp = MaskBox.Text;
            string[] arrayMIP = strTemp.Split('.');
            for (int i = 0; i < 4; i++)
            {
                Mip[i] = byte.Parse(arrayMIP[i]);
                Atemp = UInt16.Parse(arrayMIP[i]);
                if (Atemp > 255)
                {
                    MessageBox.Show("掩码输入错误"); return;
                }
                buf[10 + i] = Mip[i];
            }
            // GIPBox
            strTemp = GIPBox.Text;
            string[] arrayGIP = strTemp.Split('.');
            for (int i = 0; i < 4; i++)
            {
                Gip[i] = byte.Parse(arrayGIP[i]);
                Atemp = UInt16.Parse(arrayGIP[i]);
                if (Atemp > 255)
                {
                    MessageBox.Show("网关输入错误"); return;
                }
                buf[14 + i] = Gip[i];
            }
            //Sip
            strTemp = SeverIPBox.Text;
            string[] arraySIP = strTemp.Split('.');
            for (int i = 0; i < 4; i++)
            {
                Sip[i] = byte.Parse(arraySIP[i]);
                Atemp = UInt16.Parse(arraySIP[i]);
                if (Atemp > 255)
                {
                    MessageBox.Show("远端IP输入错误"); return;
                }
                buf[18 + i] = Sip[i];
            }

            //LPORT
            strTemp = LocalPortBox.Text;
            Lport = UInt16.Parse(strTemp);
            if (Lport > 65535)
            {
                MessageBox.Show("本地端口输入错误"); return;
            }
            buf[22] = (byte)Lport;
            buf[23] = (byte)(Lport >> 8);
            //SIP
            strTemp = SeverPortBox.Text;
            Sport = UInt16.Parse(strTemp);
            if (Sport > 65535)
            {
                MessageBox.Show("远程端口输入错误"); return;
            }
            buf[24] = (byte)Sport;
            buf[25] = (byte)(Sport >> 8);

        }
        // 网络参数 --设置
        private void bSetNetParam_Click(object sender, EventArgs e)
        {
            byte m_bDevType = 0;  //设备类型
            byte[] arrBuffer = new byte[250];
            //具体参数对应关系见相关文档
            //Get_Show_NetParmData();
            Get_Show_NetParmData(arrBuffer);  //获取初值；

            string str1 = "", str2 = "";
            for (int i = 0; i < 220; i++)
            {
                str1 = String.Format("{0:X2} ", arrBuffer[i]);
                str2 = str2 + str1;
            }
            ShowMessage("读取参数成功\r\n");
            if (!IsConnected) return;

            switch (ConnectType)
            {
                case 0:
                    if (RFID.SWComApi.SWCom_SetDeviceNetParam(m_bDevType, arrBuffer, 150) == false)
                    {
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
                case 1:
                    if (RFID.SWHidApi.SWHid_SetDeviceNetParam(m_bDevType, arrBuffer, 150) == false)
                    {
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
                case 2:
                    if (RFID.SWNetApi.SWNet_SetDeviceNetParam(m_bDevType, arrBuffer, 150) == false)
                    {
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
                case 3:
                    ShowMessage("此模式不支持操作\r\n");
                    break;
                default: break;

            }
            ShowMessage("操作成功\r\n");
        }
        private void bSetDefault_Click(object sender, EventArgs e)
        {

        }
        private void bReset_Click(object sender, EventArgs e)
        {
            byte[] arrBuffer = new byte[150];

            if (!IsConnected) return;

            switch (ConnectType)
            {
                case 0:  //COM方式
                    if (RFID.SWComApi.SWCom_RebootDevice() == false)
                    {
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
                case 1:  //USB HID方式
                    if (RFID.SWHidApi.SWHid_RebootDevice() == false)
                    {
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
                case 2:  //网络方式
                    if (RFID.SWNetApi.SWNet_RebootDevice() == false)
                    {
                        ShowMessage("操作失败\r\n");
                        return;
                    }
                    break;
            }
        }

        #endregion


        #region 排序操作
        private void ReorderBut_Click(object sender, EventArgs e)
        {
            ViewTime.Stop();
            StopInquiryButton_Click(sender, e); //button3_Click(sender, e);
            Thread.Sleep(5);
            list_IvTag = list_IvTag.OrderBy(ti =>
            {
                int id;
                return int.TryParse(ti.ID, out id) ? id : int.MinValue; // 或者选择另一个默认值  
            }).ToList();
            //ViewTime.Start();
            UpdateAllListView(list_IvTag);  //刷新listview
        }
        #endregion

        #region  导出表格
        public void ExportListViewToExcel(ListView listView, string excelFilePath)
        {
            Application excelApp = new Application();
            if (excelApp == null)
            {
                MessageBox.Show("Excel is not properly installed!");
                return;
            }

            Workbook workbook = excelApp.Workbooks.Add(XlWBATemplate.xlWBATWorksheet);
            _Worksheet worksheet = (_Worksheet)workbook.Worksheets.get_Item(1);

            // 设置列标题  
            for (int col = 0; col < listView.Columns.Count; col++)
            {
                worksheet.Cells[1, col + 1] = listView.Columns[col].Text;
            }

            // 填充数据  
            for (int row = 0; row < listView.Items.Count; row++)
            {
                for (int col = 0; col < listView.Columns.Count; col++)
                {
                    // 注意：ListViewItem.SubItems 用于访问子项（即每行的列）  
                    //worksheet.Cells[row + 2, col + 1] = listView.Items[row].SubItems[col].Text;
                    try
                    {
                        //if(col==1)
                        //{
                        //    // 读取当前行的第一列的值  
                        //    var cellValue = worksheet.Cells[row, 1].Value?.ToString();
                        //    // 尝试将值转换为整数，并格式化为十位十进制格式  
                        //    if (int.TryParse(cellValue, out int id))
                        //    {
                        //        string formattedId = id.ToString("D10"); // D10表示十进制，并填充至10位  

                        //        // 将格式化后的值写回单元格  
                        //        worksheet.Cells[row + 2, col + 1].Value = formattedId;
                        //    }
                        //}
                        //else
                           worksheet.Cells[row + 2, col + 1] = listView.Items[row].SubItems[col].Text;
                    }
                    catch (ArgumentOutOfRangeException ex)
                    {
                        Console.WriteLine($"Error accessing SubItems: {ex.Message}");
                    }
                }
            }

            // 自动调整列宽  
            worksheet.Columns.AutoFit();

            // 保存文件  
            workbook.SaveAs(excelFilePath, XlFileFormat.xlOpenXMLWorkbook, Type.Missing, Type.Missing,
                false, false, XlSaveAsAccessMode.xlNoChange, Type.Missing, Type.Missing, Type.Missing,
                Type.Missing, Type.Missing);

            // 清理  
            workbook.Close(false, Type.Missing, Type.Missing);
            excelApp.Quit();

            // 释放 COM 对象  
            //ReleaseObject(worksheet);
            //ReleaseObject(workbook);
            //ReleaseObject(excelApp);

            MessageBox.Show("Excel 文件已保存：" + excelFilePath);
        }

        private void DataOutBut_Click(object sender, EventArgs e)
        {
            ExportListViewToExcel(IvTagData, @"D:\\1.xlsx");
        }

        #endregion
    }
}
