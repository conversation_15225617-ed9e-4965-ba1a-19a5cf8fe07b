#!/usr/bin/env python3
"""
Basic usage example for CallTag Controller
声光标签控制器基本使用示例
"""

import time
import sys
import os

# Add parent directory to path to import calltag_controller
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from calltag_controller import CallTagDevice, SerialConnection, USBConnection, TCPClientConnection
from calltag_controller.core.protocol import Protocol


def on_tag_detected(tag_info):
    """Callback function when a tag is detected"""
    print(f"Tag detected: {tag_info}")


def on_error(error_msg):
    """Callback function for errors"""
    print(f"Error: {error_msg}")


def serial_example():
    """Example using serial connection"""
    print("=== Serial Connection Example ===")
    
    # Create serial connection
    serial_conn = SerialConnection('/dev/ttyUSB0', 115200)  # Linux
    # serial_conn = SerialConnection('COM3', 115200)  # Windows
    
    # Create device
    device = CallTagDevice(serial_conn)
    device.on_tag_detected = on_tag_detected
    device.on_error = on_error
    
    try:
        # Connect to device
        if device.connect():
            print("Connected to device via serial")
            
            # Start reading tags
            if device.start_reading():
                print("Started reading tags...")
                
                # Read for 10 seconds
                time.sleep(10)
                
                # Call a specific tag (ID: 123456)
                print("Calling tag 123456...")
                device.call_tag(123456, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True)
                
                # Wait 5 seconds
                time.sleep(5)
                
                # Stop calling the tag
                print("Stopping call...")
                device.stop_call_tag(123456)
                
                # Show cached tags
                cached_tags = device.get_cached_tags()
                print(f"Found {len(cached_tags)} unique tags:")
                for tag in cached_tags:
                    print(f"  {tag}")
            
            # Stop reading
            device.stop_reading()
            print("Stopped reading")
        else:
            print("Failed to connect to device")
    
    finally:
        device.disconnect()
        print("Disconnected")


def usb_example():
    """Example using USB connection"""
    print("\n=== USB Connection Example ===")
    
    # Create USB connection
    usb_conn = USBConnection()
    
    # List available devices
    devices = usb_conn.get_available_devices()
    print(f"Found {len(devices)} USB devices:")
    for i, dev in enumerate(devices):
        print(f"  {i}: {dev}")
    
    if not devices:
        print("No USB devices found")
        return
    
    # Create device
    device = CallTagDevice(usb_conn)
    device.on_tag_detected = on_tag_detected
    device.on_error = on_error
    
    try:
        # Connect to first device
        if device.connect():
            print("Connected to device via USB")
            
            # Get device info
            info = device.get_device_info()
            print(f"Device info: {info}")
            
            # Start reading tags
            if device.start_reading():
                print("Started reading tags...")
                time.sleep(5)
                
                # Show cached tags
                cached_tags = device.get_cached_tags()
                print(f"Found {len(cached_tags)} unique tags")
            
            device.stop_reading()
        else:
            print("Failed to connect to USB device")
    
    finally:
        device.disconnect()


def tcp_client_example():
    """Example using TCP client connection"""
    print("\n=== TCP Client Connection Example ===")
    
    # Create TCP client connection
    tcp_conn = TCPClientConnection('192.168.1.250', 60000)
    
    # Create device
    device = CallTagDevice(tcp_conn)
    device.on_tag_detected = on_tag_detected
    device.on_error = on_error
    
    try:
        # Connect to device
        if device.connect():
            print("Connected to device via TCP")
            
            # Start reading tags
            if device.start_reading():
                print("Started reading tags...")
                time.sleep(10)
                
                # Call a tag
                device.call_tag(123456, Protocol.LED_ALTERNATE_MODE, Protocol.TIME_60_SEC)
                time.sleep(3)
                device.stop_call_tag(123456)
            
            device.stop_reading()
        else:
            print("Failed to connect to TCP device")
    
    finally:
        device.disconnect()


def main():
    """Main function to run examples"""
    print("CallTag Controller Examples")
    print("Choose connection type:")
    print("1. Serial")
    print("2. USB")
    print("3. TCP Client")
    print("4. Run all examples")
    
    try:
        choice = input("Enter choice (1-4): ").strip()
        
        if choice == '1':
            serial_example()
        elif choice == '2':
            usb_example()
        elif choice == '3':
            tcp_client_example()
        elif choice == '4':
            serial_example()
            usb_example()
            tcp_client_example()
        else:
            print("Invalid choice")
    
    except KeyboardInterrupt:
        print("\nExiting...")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == '__main__':
    main()
