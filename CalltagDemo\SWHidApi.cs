﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;

namespace RFID
{
    public class SWHidApi
    {
        /******** 功能: HID函数:DLL 初始化函数，使用HID必须在初始化函数中调用此函数**********/
        //  返回: 无
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void SWHid_DllInt();

        /******** 功能: HID函数:获取当前接入的USB数量**********/
        //  返回: 如果没有就返回0, 有的话返回数量
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int SWHid_GetUsbCount();

        /******** 功能: HID函数:获取当前接入的USB HID值**********/
        //  参数: iIndex：          HID序号 0,1,2....
        //        pucDeviceInfo：   HID值
        //  返回: 如果没有就返回0，有的话返回数量
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_GetUsbInfo(UInt16 iIndex, byte[] pucDeviceInfo);

        /******** 功能: HID函数:打开设备 *******************************/
        //  参数: iIndex：HID序号 0,1,2....
        //  返回: 成功则返回句柄, 失败则返回NULL
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_OpenDevice(UInt16 iIndex);

        /******** 功能: HID函数:关闭设备 *******************************/
        //  参数: 无
        //  返回: 无
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void SWHid_CloseDevice();

        /******** 功能: HID函数:重启设备**********/
        //  参数: 无
        //  返回: 成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_RebootDevice();

        /******** 功能: HID函数:开始读卡**********/
        //  参数: 无
        //  返回: 成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_StartRead();

        /******** 功能: HID函数:停止读卡**********/
        //  参数: 无
        //  返回: 成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_StopRead();

        /******** 功能: HID函数:得到设备信息. 9个字节**********/
        //  参数: pucSystemInfo：返回的设备信息  9个字节, 1:软件版本 2:硬件版本 3 - 9:设备SN
        //  返回: 成功则返回1, 失败则返回0


        //打开考勤
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_StartAttenceRead();

        //关闭考勤
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_StopAttenceRead();

        //打开答题
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_StartAnswerRead();

        //关闭答题
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_StopAnswerRead();


        //分值操作指令;
        // [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        // public static extern bool SWHid_Gread_operation(byte[] pParam, byte num);


        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_GetDeviceSystemInfo(byte[] pucSystemInfo);

        /******** 功能：HID函数:得到设备参数**********/
        //  参数：pucDeviceType： 返回的设备类型, 具体见参数手册			
        //        pucDeviceParam：返回的设备参数, 具体见参数手册	
        //        ucParamLength： 返回的参数长度 
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_ReadDeviceParam(out byte pucDeviceType, byte[] pucDeviceParam, byte ucParamLength);

        /******** 功能: HID函数:设置设备参数**********/
        //  参数: ucDeviceType：  设备类型, 必须与设备匹配才能设置成功			
        //        pucDeviceParam：设备参数, 具体见参数应手册	
        //        ucParamLength： 参数长度 
        //  返回: 成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_SetDeviceParam(byte pucDeviceType, byte[] pucDeviceParam, byte ucParamLength);

        /******** 功能：串口函数:读设备设备网络参数**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_ReadDeviceNetParam(out byte pucDeviceType, byte[] pucDeviceParam, byte ucParamLength);


        /******** 功能：串口函数:设置网络参数**********/
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_SetDeviceNetParam(byte pucDeviceType, byte[] pucDeviceParam, byte ucParamLength);


        /******** 功能: HID函数:恢复默认设备参数**********/
        //  参数: 无
        //  返回: 成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_DefaultDeviceParam();

        /******** 功能: HID函数:读取设备时间**********/
        //  参数: pucDeviceTime：6个字节的时间 分别为 年 月 日 时 分 秒  16进制格式	
        //  返回: 成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_ReadDeviceTime(byte[] pucDeviceTime);

        /******** 功能: HID函数:设置设备时间**********/
        //  参数: pucDeviceTime：6个字节的时间 分别为 年 月 日 时 分 秒  16进制格式	
        //  返回: 成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_SetDeviceTime(byte[] pucDeviceTime);

        /******** 功能：HID函数:读取白名单**********/
        //  参数：pucWhiteList：4个字节一组的 ID号
        //        piWhiteListNum：ID号个数
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_ReadWhiteList(byte[] pucWhiteList, out UInt16 piWhiteListNum);

        /******** 功能：HID函数:设置白名单**********/
        //  参数：pucWhiteList：4个字节一组的 ID号
        //        piWhiteListNum：ID号个数
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_WriteWhiteList(byte[] pucWhiteList, UInt16 iWhiteListNum);

        /******** 功能：HID函数:删除所有白名单**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_DeleteAllWhiteList();

        /******** 功能：HID函数:删除单条白名单**********/
        //  参数：pucWhiteList: 4个字节一组的 ID号
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_DeleteOneWhiteList(byte[] pucWhiteList);

        /******** 功能：HID函数:获取缓存卡号的数据，命令方式用**********/
        //  参数：hDeviceHandle：设备句柄
        //        pucDevCache：   缓存数据, 以JSON格式      
        //        piDevCacheNumber：数据总条数
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_GetDevCache(byte[] pucDevJsonCache, out UInt16 piDevCacheLength);

        /******** 功能：HID函数:删除缓存数据**********/
        //  参数：无
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_DelDevCacheCount();

        /******** 功能：HID函数:初始化回调函数**********/
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_InitCallBack();

        /******** 功能：HID函数:释放回调函数，关闭回调函数后必须释放，否则内存泄漏**********/
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_ReleaseCallBack();

        /******** 功能：HID函数:开启回调函数的接收线程**********/
        //  返回：成功则返回1, 失败则返回0
        /*********************************************************/
        //[DllImport("SWHidApi.dll")]
        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_StartRecvThread();



        // 搜索指令;
        // 20230426 添加开始搜索/停止搜索

        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_FindID(byte[] pucDeviceID, byte s_buz_led, byte s_time);


        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_DisFindID( byte[] pucDeviceID);


        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SWHid_HardChoice(byte temp);

        /*
        * 回调函数原型
        * msg == 1：表示设备被拔出
        * msg == 2：param1表示答案条数，param2表示答案字符串
        * param2以JSON格式存在 例如 {"DevSN":"22041607214089","AnswerKey":[{"ID":"00000000","Key":"DC","Num":"1","Time":"08:52:46"},{"ID":"00000000","Key":"ABCD","Num":"1","Time":"08:52:50"}]}
        */

        [UnmanagedFunctionPointer(CallingConvention.Cdecl)] 
        public delegate void CallbackDelegate(int msg, int param1, string param2);


        [DllImport("SWHidApi.dll", CallingConvention = CallingConvention.Cdecl)]
        internal static extern int SWHid_SetCallbackAddrWithJsonNoSort(CallbackDelegate pfAddr);  //以JSON格式输出

    }
}
