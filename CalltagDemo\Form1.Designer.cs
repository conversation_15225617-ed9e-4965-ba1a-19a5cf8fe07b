﻿
namespace CalltagDemo
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel1 = new System.Windows.Forms.Panel();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rdTCPClient = new System.Windows.Forms.RadioButton();
            this.rdTCPSever = new System.Windows.Forms.RadioButton();
            this.rdUart = new System.Windows.Forms.RadioButton();
            this.rdUSB = new System.Windows.Forms.RadioButton();
            this.panel2 = new System.Windows.Forms.Panel();
            this.gUSB = new System.Windows.Forms.GroupBox();
            this.cbUSB = new System.Windows.Forms.ComboBox();
            this.label1 = new System.Windows.Forms.Label();
            this.gTCPClient = new System.Windows.Forms.GroupBox();
            this.CportBox = new System.Windows.Forms.TextBox();
            this.label5 = new System.Windows.Forms.Label();
            this.CIpBox = new System.Windows.Forms.TextBox();
            this.label4 = new System.Windows.Forms.Label();
            this.gUART = new System.Windows.Forms.GroupBox();
            this.COMBox = new System.Windows.Forms.ComboBox();
            this.label3 = new System.Windows.Forms.Label();
            this.gTcpSever = new System.Windows.Forms.GroupBox();
            this.SPortBox = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.panel3 = new System.Windows.Forms.Panel();
            this.panel5 = new System.Windows.Forms.Panel();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.jytConn = new System.Windows.Forms.ListBox();
            this.button1 = new System.Windows.Forms.Button();
            this.MesBox = new System.Windows.Forms.TextBox();
            this.groupBox4 = new System.Windows.Forms.GroupBox();
            this.gList = new System.Windows.Forms.GroupBox();
            this.radioHex = new System.Windows.Forms.RadioButton();
            this.radioDec = new System.Windows.Forms.RadioButton();
            this.DataOutBut = new System.Windows.Forms.Button();
            this.ReorderBut = new System.Windows.Forms.Button();
            this.TagNumlabel = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.groupBox5 = new System.Windows.Forms.GroupBox();
            this.comboBox3 = new System.Windows.Forms.ComboBox();
            this.label7 = new System.Windows.Forms.Label();
            this.checkBuz = new System.Windows.Forms.CheckBox();
            this.comboBox2 = new System.Windows.Forms.ComboBox();
            this.label8 = new System.Windows.Forms.Label();
            this.FindIdBox = new System.Windows.Forms.TextBox();
            this.label6 = new System.Windows.Forms.Label();
            this.StopFindbutton = new System.Windows.Forms.Button();
            this.Findbutton = new System.Windows.Forms.Button();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.Clearbutton = new System.Windows.Forms.Button();
            this.StopInquiryButton = new System.Windows.Forms.Button();
            this.InquiryButton = new System.Windows.Forms.Button();
            this.panel6 = new System.Windows.Forms.Panel();
            this.panel4 = new System.Windows.Forms.Panel();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.IvTagData = new System.Windows.Forms.ListView();
            this.columnHeader8 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader9 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader10 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader11 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader12 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader13 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader14 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.gbNetParam = new System.Windows.Forms.GroupBox();
            this.label14 = new System.Windows.Forms.Label();
            this.TickBox = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.DNScheckBox = new System.Windows.Forms.CheckBox();
            this.bReset = new System.Windows.Forms.Button();
            this.bSetDefault = new System.Windows.Forms.Button();
            this.bSetNetParam = new System.Windows.Forms.Button();
            this.bReadNetParam = new System.Windows.Forms.Button();
            this.WIFIBox = new System.Windows.Forms.TextBox();
            this.textBox2 = new System.Windows.Forms.TextBox();
            this.DNSBox = new System.Windows.Forms.TextBox();
            this.DHCPcheckBox = new System.Windows.Forms.CheckBox();
            this.label28 = new System.Windows.Forms.Label();
            this.label27 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.SeverPortBox = new System.Windows.Forms.TextBox();
            this.LocalPortBox = new System.Windows.Forms.TextBox();
            this.GIPBox = new System.Windows.Forms.TextBox();
            this.label23 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.SeverIPBox = new System.Windows.Forms.TextBox();
            this.label20 = new System.Windows.Forms.Label();
            this.MaskBox = new System.Windows.Forms.TextBox();
            this.label21 = new System.Windows.Forms.Label();
            this.label24 = new System.Windows.Forms.Label();
            this.IPBox = new System.Windows.Forms.TextBox();
            this.MacBox = new System.Windows.Forms.TextBox();
            this.label25 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.gbSysParam = new System.Windows.Forms.GroupBox();
            this.label15 = new System.Windows.Forms.Label();
            this.cbList = new System.Windows.Forms.CheckBox();
            this.label22 = new System.Windows.Forms.Label();
            this.cbPortMode = new System.Windows.Forms.ComboBox();
            this.cbBuz = new System.Windows.Forms.CheckBox();
            this.tbTransMode = new System.Windows.Forms.TextBox();
            this.tbAddr = new System.Windows.Forms.TextBox();
            this.tbPower = new System.Windows.Forms.TextBox();
            this.tbFilterTime = new System.Windows.Forms.TextBox();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.label17 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.bReadDevice = new System.Windows.Forms.Button();
            this.bSetDevice = new System.Windows.Forms.Button();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.ViewTime = new System.Windows.Forms.Timer(this.components);
            this.panel7 = new System.Windows.Forms.Panel();
            this.groupBox6 = new System.Windows.Forms.GroupBox();
            this.ConnectButton = new System.Windows.Forms.Button();
            this.ScanButton = new System.Windows.Forms.Button();
            this.columnHeader1 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader2 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader3 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader4 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader5 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader6 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader7 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.panel1.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.panel2.SuspendLayout();
            this.gUSB.SuspendLayout();
            this.gTCPClient.SuspendLayout();
            this.gUART.SuspendLayout();
            this.gTcpSever.SuspendLayout();
            this.panel3.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox4.SuspendLayout();
            this.gList.SuspendLayout();
            this.groupBox5.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.panel6.SuspendLayout();
            this.panel4.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.gbNetParam.SuspendLayout();
            this.gbSysParam.SuspendLayout();
            this.groupBox6.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.groupBox1);
            this.panel1.Location = new System.Drawing.Point(15, 16);
            this.panel1.Margin = new System.Windows.Forms.Padding(4);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(306, 96);
            this.panel1.TabIndex = 0;
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rdTCPClient);
            this.groupBox1.Controls.Add(this.rdTCPSever);
            this.groupBox1.Controls.Add(this.rdUart);
            this.groupBox1.Controls.Add(this.rdUSB);
            this.groupBox1.Location = new System.Drawing.Point(3, 3);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(300, 90);
            this.groupBox1.TabIndex = 0;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "端口选择";
            // 
            // rdTCPClient
            // 
            this.rdTCPClient.AutoSize = true;
            this.rdTCPClient.Location = new System.Drawing.Point(135, 60);
            this.rdTCPClient.Name = "rdTCPClient";
            this.rdTCPClient.Size = new System.Drawing.Size(120, 24);
            this.rdTCPClient.TabIndex = 3;
            this.rdTCPClient.TabStop = true;
            this.rdTCPClient.Text = "TCP本地端";
            this.rdTCPClient.UseVisualStyleBackColor = true;
            this.rdTCPClient.CheckedChanged += new System.EventHandler(this.rdTCPClient_CheckedChanged);
            // 
            // rdTCPSever
            // 
            this.rdTCPSever.AutoSize = true;
            this.rdTCPSever.Location = new System.Drawing.Point(7, 60);
            this.rdTCPSever.Name = "rdTCPSever";
            this.rdTCPSever.Size = new System.Drawing.Size(120, 24);
            this.rdTCPSever.TabIndex = 2;
            this.rdTCPSever.TabStop = true;
            this.rdTCPSever.Text = "TCP服务端";
            this.rdTCPSever.UseVisualStyleBackColor = true;
            this.rdTCPSever.CheckedChanged += new System.EventHandler(this.rdTCPSever_CheckedChanged);
            // 
            // rdUart
            // 
            this.rdUart.AutoSize = true;
            this.rdUart.Location = new System.Drawing.Point(135, 30);
            this.rdUart.Name = "rdUart";
            this.rdUart.Size = new System.Drawing.Size(70, 24);
            this.rdUart.TabIndex = 1;
            this.rdUart.TabStop = true;
            this.rdUart.Text = "串口";
            this.rdUart.UseVisualStyleBackColor = true;
            this.rdUart.CheckedChanged += new System.EventHandler(this.rdUart_CheckedChanged);
            // 
            // rdUSB
            // 
            this.rdUSB.AutoSize = true;
            this.rdUSB.Location = new System.Drawing.Point(7, 30);
            this.rdUSB.Name = "rdUSB";
            this.rdUSB.Size = new System.Drawing.Size(60, 24);
            this.rdUSB.TabIndex = 0;
            this.rdUSB.TabStop = true;
            this.rdUSB.Text = "USB";
            this.rdUSB.UseVisualStyleBackColor = true;
            this.rdUSB.CheckedChanged += new System.EventHandler(this.rdUSB_CheckedChanged);
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.gUSB);
            this.panel2.Location = new System.Drawing.Point(13, 116);
            this.panel2.Margin = new System.Windows.Forms.Padding(4);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(305, 100);
            this.panel2.TabIndex = 1;
            // 
            // gUSB
            // 
            this.gUSB.Controls.Add(this.cbUSB);
            this.gUSB.Controls.Add(this.label1);
            this.gUSB.Location = new System.Drawing.Point(2, 1);
            this.gUSB.Name = "gUSB";
            this.gUSB.Size = new System.Drawing.Size(292, 95);
            this.gUSB.TabIndex = 1;
            this.gUSB.TabStop = false;
            this.gUSB.Text = "USB连接";
            // 
            // cbUSB
            // 
            this.cbUSB.FormattingEnabled = true;
            this.cbUSB.Location = new System.Drawing.Point(65, 35);
            this.cbUSB.Name = "cbUSB";
            this.cbUSB.Size = new System.Drawing.Size(172, 28);
            this.cbUSB.TabIndex = 5;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 39);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(49, 20);
            this.label1.TabIndex = 0;
            this.label1.Text = "USB:";
            // 
            // gTCPClient
            // 
            this.gTCPClient.Controls.Add(this.CportBox);
            this.gTCPClient.Controls.Add(this.label5);
            this.gTCPClient.Controls.Add(this.CIpBox);
            this.gTCPClient.Controls.Add(this.label4);
            this.gTCPClient.Location = new System.Drawing.Point(14, 111);
            this.gTCPClient.Name = "gTCPClient";
            this.gTCPClient.Size = new System.Drawing.Size(303, 95);
            this.gTCPClient.TabIndex = 1;
            this.gTCPClient.TabStop = false;
            this.gTCPClient.Text = "Tcp本地端";
            // 
            // CportBox
            // 
            this.CportBox.Location = new System.Drawing.Point(62, 55);
            this.CportBox.Name = "CportBox";
            this.CportBox.Size = new System.Drawing.Size(102, 30);
            this.CportBox.TabIndex = 5;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(6, 58);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(69, 20);
            this.label5.TabIndex = 4;
            this.label5.Text = "端口：";
            // 
            // CIpBox
            // 
            this.CIpBox.Location = new System.Drawing.Point(62, 19);
            this.CIpBox.Name = "CIpBox";
            this.CIpBox.Size = new System.Drawing.Size(173, 30);
            this.CIpBox.TabIndex = 3;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(7, 29);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(49, 20);
            this.label4.TabIndex = 3;
            this.label4.Text = "IP：";
            // 
            // gUART
            // 
            this.gUART.Controls.Add(this.COMBox);
            this.gUART.Controls.Add(this.label3);
            this.gUART.Location = new System.Drawing.Point(13, 111);
            this.gUART.Name = "gUART";
            this.gUART.Size = new System.Drawing.Size(307, 95);
            this.gUART.TabIndex = 1;
            this.gUART.TabStop = false;
            this.gUART.Text = "串口连接";
            // 
            // COMBox
            // 
            this.COMBox.FormattingEnabled = true;
            this.COMBox.Items.AddRange(new object[] {
            "COM1",
            "COM2",
            "COM3",
            "COM4",
            "COM5",
            "COM6",
            "COM7",
            "COM8",
            "COM9",
            "COM10",
            "COM11",
            "COM12",
            "COM13",
            "COM14",
            "COM15",
            "COM16",
            "COM17",
            "COM18",
            "COM19"});
            this.COMBox.Location = new System.Drawing.Point(86, 34);
            this.COMBox.Name = "COMBox";
            this.COMBox.Size = new System.Drawing.Size(121, 28);
            this.COMBox.TabIndex = 4;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(11, 42);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(69, 20);
            this.label3.TabIndex = 3;
            this.label3.Text = "端口：";
            // 
            // gTcpSever
            // 
            this.gTcpSever.Controls.Add(this.SPortBox);
            this.gTcpSever.Controls.Add(this.label2);
            this.gTcpSever.Location = new System.Drawing.Point(12, 114);
            this.gTcpSever.Name = "gTcpSever";
            this.gTcpSever.Size = new System.Drawing.Size(303, 92);
            this.gTcpSever.TabIndex = 1;
            this.gTcpSever.TabStop = false;
            this.gTcpSever.Text = "Tcp侦听";
            // 
            // SPortBox
            // 
            this.SPortBox.Location = new System.Drawing.Point(72, 42);
            this.SPortBox.Name = "SPortBox";
            this.SPortBox.Size = new System.Drawing.Size(100, 30);
            this.SPortBox.TabIndex = 3;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(7, 45);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(69, 20);
            this.label2.TabIndex = 0;
            this.label2.Text = "端口：";
            // 
            // panel3
            // 
            this.panel3.Controls.Add(this.panel5);
            this.panel3.Location = new System.Drawing.Point(15, 304);
            this.panel3.Margin = new System.Windows.Forms.Padding(4);
            this.panel3.Name = "panel3";
            this.panel3.Size = new System.Drawing.Size(306, 400);
            this.panel3.TabIndex = 2;
            // 
            // panel5
            // 
            this.panel5.Location = new System.Drawing.Point(413, 473);
            this.panel5.Margin = new System.Windows.Forms.Padding(4);
            this.panel5.Name = "panel5";
            this.panel5.Size = new System.Drawing.Size(834, 135);
            this.panel5.TabIndex = 4;
            // 
            // groupBox3
            // 
            this.groupBox3.Controls.Add(this.jytConn);
            this.groupBox3.Controls.Add(this.button1);
            this.groupBox3.Controls.Add(this.MesBox);
            this.groupBox3.Location = new System.Drawing.Point(13, 277);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(303, 424);
            this.groupBox3.TabIndex = 1;
            this.groupBox3.TabStop = false;
            this.groupBox3.Text = "日志";
            // 
            // jytConn
            // 
            this.jytConn.DisplayMember = "Name";
            this.jytConn.FormattingEnabled = true;
            this.jytConn.ItemHeight = 20;
            this.jytConn.Location = new System.Drawing.Point(5, 20);
            this.jytConn.Margin = new System.Windows.Forms.Padding(4);
            this.jytConn.Name = "jytConn";
            this.jytConn.SelectionMode = System.Windows.Forms.SelectionMode.MultiExtended;
            this.jytConn.Size = new System.Drawing.Size(291, 84);
            this.jytConn.TabIndex = 54;
            // 
            // button1
            // 
            this.button1.BackColor = System.Drawing.SystemColors.ControlLight;
            this.button1.Font = new System.Drawing.Font("宋体", 10.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.button1.Location = new System.Drawing.Point(11, 391);
            this.button1.Name = "button1";
            this.button1.Size = new System.Drawing.Size(92, 30);
            this.button1.TabIndex = 6;
            this.button1.Text = "清除";
            this.button1.UseVisualStyleBackColor = false;
            this.button1.Click += new System.EventHandler(this.button1_Click);
            // 
            // MesBox
            // 
            this.MesBox.Location = new System.Drawing.Point(3, 111);
            this.MesBox.Multiline = true;
            this.MesBox.Name = "MesBox";
            this.MesBox.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.MesBox.Size = new System.Drawing.Size(294, 269);
            this.MesBox.TabIndex = 0;
            // 
            // groupBox4
            // 
            this.groupBox4.Controls.Add(this.gList);
            this.groupBox4.Controls.Add(this.groupBox5);
            this.groupBox4.Controls.Add(this.groupBox2);
            this.groupBox4.Location = new System.Drawing.Point(3, 3);
            this.groupBox4.Name = "groupBox4";
            this.groupBox4.Size = new System.Drawing.Size(793, 172);
            this.groupBox4.TabIndex = 0;
            this.groupBox4.TabStop = false;
            this.groupBox4.Text = "接收器操作";
            // 
            // gList
            // 
            this.gList.Controls.Add(this.radioHex);
            this.gList.Controls.Add(this.radioDec);
            this.gList.Controls.Add(this.DataOutBut);
            this.gList.Controls.Add(this.ReorderBut);
            this.gList.Controls.Add(this.TagNumlabel);
            this.gList.Controls.Add(this.label9);
            this.gList.Location = new System.Drawing.Point(544, 23);
            this.gList.Name = "gList";
            this.gList.Size = new System.Drawing.Size(243, 143);
            this.gList.TabIndex = 4;
            this.gList.TabStop = false;
            this.gList.Text = "表格操作";
            // 
            // radioHex
            // 
            this.radioHex.AutoSize = true;
            this.radioHex.Location = new System.Drawing.Point(135, 91);
            this.radioHex.Name = "radioHex";
            this.radioHex.Size = new System.Drawing.Size(110, 24);
            this.radioHex.TabIndex = 51;
            this.radioHex.TabStop = true;
            this.radioHex.Text = "十六进制";
            this.radioHex.UseVisualStyleBackColor = true;
            // 
            // radioDec
            // 
            this.radioDec.AutoSize = true;
            this.radioDec.Location = new System.Drawing.Point(135, 56);
            this.radioDec.Name = "radioDec";
            this.radioDec.Size = new System.Drawing.Size(90, 24);
            this.radioDec.TabIndex = 50;
            this.radioDec.TabStop = true;
            this.radioDec.Text = "十进制";
            this.radioDec.UseVisualStyleBackColor = true;
            // 
            // DataOutBut
            // 
            this.DataOutBut.BackColor = System.Drawing.SystemColors.ControlLight;
            this.DataOutBut.Location = new System.Drawing.Point(6, 87);
            this.DataOutBut.Name = "DataOutBut";
            this.DataOutBut.Size = new System.Drawing.Size(109, 32);
            this.DataOutBut.TabIndex = 49;
            this.DataOutBut.Text = "数据导出";
            this.DataOutBut.UseVisualStyleBackColor = false;
            this.DataOutBut.Click += new System.EventHandler(this.DataOutBut_Click);
            // 
            // ReorderBut
            // 
            this.ReorderBut.BackColor = System.Drawing.SystemColors.ControlLight;
            this.ReorderBut.Location = new System.Drawing.Point(6, 52);
            this.ReorderBut.Name = "ReorderBut";
            this.ReorderBut.Size = new System.Drawing.Size(109, 32);
            this.ReorderBut.TabIndex = 48;
            this.ReorderBut.Text = "排序操作";
            this.ReorderBut.UseVisualStyleBackColor = false;
            this.ReorderBut.Click += new System.EventHandler(this.ReorderBut_Click);
            // 
            // TagNumlabel
            // 
            this.TagNumlabel.AutoSize = true;
            this.TagNumlabel.Location = new System.Drawing.Point(196, 24);
            this.TagNumlabel.Name = "TagNumlabel";
            this.TagNumlabel.Size = new System.Drawing.Size(19, 20);
            this.TagNumlabel.TabIndex = 3;
            this.TagNumlabel.Text = "0";
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(131, 24);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(69, 20);
            this.label9.TabIndex = 2;
            this.label9.Text = "数量：";
            // 
            // groupBox5
            // 
            this.groupBox5.Controls.Add(this.comboBox3);
            this.groupBox5.Controls.Add(this.label7);
            this.groupBox5.Controls.Add(this.checkBuz);
            this.groupBox5.Controls.Add(this.comboBox2);
            this.groupBox5.Controls.Add(this.label8);
            this.groupBox5.Controls.Add(this.FindIdBox);
            this.groupBox5.Controls.Add(this.label6);
            this.groupBox5.Controls.Add(this.StopFindbutton);
            this.groupBox5.Controls.Add(this.Findbutton);
            this.groupBox5.Location = new System.Drawing.Point(151, 23);
            this.groupBox5.Name = "groupBox5";
            this.groupBox5.Size = new System.Drawing.Size(387, 143);
            this.groupBox5.TabIndex = 1;
            this.groupBox5.TabStop = false;
            this.groupBox5.Text = "呼叫操作";
            // 
            // comboBox3
            // 
            this.comboBox3.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox3.FormattingEnabled = true;
            this.comboBox3.Location = new System.Drawing.Point(218, 85);
            this.comboBox3.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox3.Name = "comboBox3";
            this.comboBox3.Size = new System.Drawing.Size(160, 25);
            this.comboBox3.TabIndex = 47;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label7.Location = new System.Drawing.Point(122, 85);
            this.label7.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(109, 20);
            this.label7.TabIndex = 46;
            this.label7.Text = "时长选择：";
            // 
            // checkBuz
            // 
            this.checkBuz.AutoSize = true;
            this.checkBuz.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.checkBuz.Location = new System.Drawing.Point(218, 119);
            this.checkBuz.Margin = new System.Windows.Forms.Padding(4);
            this.checkBuz.Name = "checkBuz";
            this.checkBuz.Size = new System.Drawing.Size(131, 24);
            this.checkBuz.TabIndex = 43;
            this.checkBuz.Text = "蜂鸣器使能";
            this.checkBuz.UseVisualStyleBackColor = true;
            // 
            // comboBox2
            // 
            this.comboBox2.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.comboBox2.FormattingEnabled = true;
            this.comboBox2.Location = new System.Drawing.Point(218, 52);
            this.comboBox2.Margin = new System.Windows.Forms.Padding(4);
            this.comboBox2.Name = "comboBox2";
            this.comboBox2.Size = new System.Drawing.Size(160, 25);
            this.comboBox2.TabIndex = 45;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label8.Location = new System.Drawing.Point(122, 52);
            this.label8.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(109, 20);
            this.label8.TabIndex = 44;
            this.label8.Text = "灯光模式：";
            // 
            // FindIdBox
            // 
            this.FindIdBox.Location = new System.Drawing.Point(218, 14);
            this.FindIdBox.Name = "FindIdBox";
            this.FindIdBox.Size = new System.Drawing.Size(160, 30);
            this.FindIdBox.TabIndex = 3;
            // 
            // label6
            // 
            this.label6.AutoSize = true;
            this.label6.Location = new System.Drawing.Point(132, 18);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(89, 20);
            this.label6.TabIndex = 7;
            this.label6.Text = "标签ID：";
            // 
            // StopFindbutton
            // 
            this.StopFindbutton.BackColor = System.Drawing.SystemColors.ControlLight;
            this.StopFindbutton.Location = new System.Drawing.Point(6, 69);
            this.StopFindbutton.Name = "StopFindbutton";
            this.StopFindbutton.Size = new System.Drawing.Size(109, 32);
            this.StopFindbutton.TabIndex = 6;
            this.StopFindbutton.Text = "停止搜索";
            this.StopFindbutton.UseVisualStyleBackColor = false;
            this.StopFindbutton.Click += new System.EventHandler(this.StopFindbutton_Click);
            // 
            // Findbutton
            // 
            this.Findbutton.BackColor = System.Drawing.SystemColors.ControlLight;
            this.Findbutton.Location = new System.Drawing.Point(6, 29);
            this.Findbutton.Name = "Findbutton";
            this.Findbutton.Size = new System.Drawing.Size(109, 32);
            this.Findbutton.TabIndex = 5;
            this.Findbutton.Text = "开始搜索";
            this.Findbutton.UseVisualStyleBackColor = false;
            this.Findbutton.Click += new System.EventHandler(this.Findbutton_Click);
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.Clearbutton);
            this.groupBox2.Controls.Add(this.StopInquiryButton);
            this.groupBox2.Controls.Add(this.InquiryButton);
            this.groupBox2.Location = new System.Drawing.Point(6, 23);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(139, 143);
            this.groupBox2.TabIndex = 0;
            this.groupBox2.TabStop = false;
            this.groupBox2.Text = "询盘操作";
            // 
            // Clearbutton
            // 
            this.Clearbutton.BackColor = System.Drawing.SystemColors.ControlLight;
            this.Clearbutton.Location = new System.Drawing.Point(15, 95);
            this.Clearbutton.Name = "Clearbutton";
            this.Clearbutton.Size = new System.Drawing.Size(109, 34);
            this.Clearbutton.TabIndex = 5;
            this.Clearbutton.Text = "清除列表";
            this.Clearbutton.UseVisualStyleBackColor = false;
            this.Clearbutton.Click += new System.EventHandler(this.Clearbutton_Click);
            // 
            // StopInquiryButton
            // 
            this.StopInquiryButton.BackColor = System.Drawing.SystemColors.ControlLight;
            this.StopInquiryButton.Location = new System.Drawing.Point(15, 56);
            this.StopInquiryButton.Name = "StopInquiryButton";
            this.StopInquiryButton.Size = new System.Drawing.Size(109, 32);
            this.StopInquiryButton.TabIndex = 4;
            this.StopInquiryButton.Text = "停止询盘";
            this.StopInquiryButton.UseVisualStyleBackColor = false;
            this.StopInquiryButton.Click += new System.EventHandler(this.StopInquiryButton_Click);
            // 
            // InquiryButton
            // 
            this.InquiryButton.BackColor = System.Drawing.SystemColors.ControlLight;
            this.InquiryButton.Location = new System.Drawing.Point(15, 18);
            this.InquiryButton.Name = "InquiryButton";
            this.InquiryButton.Size = new System.Drawing.Size(109, 32);
            this.InquiryButton.TabIndex = 3;
            this.InquiryButton.Text = "开始询盘";
            this.InquiryButton.UseVisualStyleBackColor = false;
            this.InquiryButton.Click += new System.EventHandler(this.InquiryButton_Click);
            // 
            // panel6
            // 
            this.panel6.BackColor = System.Drawing.SystemColors.Control;
            this.panel6.Controls.Add(this.groupBox4);
            this.panel6.Location = new System.Drawing.Point(332, 526);
            this.panel6.Name = "panel6";
            this.panel6.Size = new System.Drawing.Size(799, 178);
            this.panel6.TabIndex = 4;
            // 
            // panel4
            // 
            this.panel4.BackColor = System.Drawing.SystemColors.GradientInactiveCaption;
            this.panel4.Controls.Add(this.tabControl1);
            this.panel4.Location = new System.Drawing.Point(333, 14);
            this.panel4.Margin = new System.Windows.Forms.Padding(4);
            this.panel4.Name = "panel4";
            this.panel4.Size = new System.Drawing.Size(803, 506);
            this.panel4.TabIndex = 3;
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage1);
            this.tabControl1.Controls.Add(this.tabPage2);
            this.tabControl1.Location = new System.Drawing.Point(3, 5);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(799, 498);
            this.tabControl1.TabIndex = 0;
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.IvTagData);
            this.tabPage1.Location = new System.Drawing.Point(4, 30);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage1.Size = new System.Drawing.Size(791, 464);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "询盘数据";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // IvTagData
            // 
            this.IvTagData.BackColor = System.Drawing.SystemColors.Window;
            this.IvTagData.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader8,
            this.columnHeader9,
            this.columnHeader10,
            this.columnHeader11,
            this.columnHeader12,
            this.columnHeader13,
            this.columnHeader14});
            this.IvTagData.FullRowSelect = true;
            this.IvTagData.GridLines = true;
            this.IvTagData.HideSelection = false;
            this.IvTagData.Location = new System.Drawing.Point(3, 5);
            this.IvTagData.Name = "IvTagData";
            this.IvTagData.Size = new System.Drawing.Size(785, 455);
            this.IvTagData.TabIndex = 1;
            this.IvTagData.UseCompatibleStateImageBehavior = false;
            this.IvTagData.View = System.Windows.Forms.View.Details;
            // 
            // columnHeader8
            // 
            this.columnHeader8.Text = "序号";
            // 
            // columnHeader9
            // 
            this.columnHeader9.Text = "标签ID";
            this.columnHeader9.Width = 180;
            // 
            // columnHeader10
            // 
            this.columnHeader10.Text = "Rssi";
            this.columnHeader10.Width = 80;
            // 
            // columnHeader11
            // 
            this.columnHeader11.Text = "状态字";
            this.columnHeader11.Width = 80;
            // 
            // columnHeader12
            // 
            this.columnHeader12.Text = "标签类型";
            this.columnHeader12.Width = 120;
            // 
            // columnHeader13
            // 
            this.columnHeader13.Text = "次数";
            this.columnHeader13.Width = 80;
            // 
            // columnHeader14
            // 
            this.columnHeader14.Text = "时间";
            this.columnHeader14.Width = 180;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.gbNetParam);
            this.tabPage2.Controls.Add(this.gbSysParam);
            this.tabPage2.Location = new System.Drawing.Point(4, 30);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage2.Size = new System.Drawing.Size(791, 464);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "设备设置";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // gbNetParam
            // 
            this.gbNetParam.Controls.Add(this.label14);
            this.gbNetParam.Controls.Add(this.TickBox);
            this.gbNetParam.Controls.Add(this.label10);
            this.gbNetParam.Controls.Add(this.DNScheckBox);
            this.gbNetParam.Controls.Add(this.bReset);
            this.gbNetParam.Controls.Add(this.bSetDefault);
            this.gbNetParam.Controls.Add(this.bSetNetParam);
            this.gbNetParam.Controls.Add(this.bReadNetParam);
            this.gbNetParam.Controls.Add(this.WIFIBox);
            this.gbNetParam.Controls.Add(this.textBox2);
            this.gbNetParam.Controls.Add(this.DNSBox);
            this.gbNetParam.Controls.Add(this.DHCPcheckBox);
            this.gbNetParam.Controls.Add(this.label28);
            this.gbNetParam.Controls.Add(this.label27);
            this.gbNetParam.Controls.Add(this.label29);
            this.gbNetParam.Controls.Add(this.SeverPortBox);
            this.gbNetParam.Controls.Add(this.LocalPortBox);
            this.gbNetParam.Controls.Add(this.GIPBox);
            this.gbNetParam.Controls.Add(this.label23);
            this.gbNetParam.Controls.Add(this.label19);
            this.gbNetParam.Controls.Add(this.SeverIPBox);
            this.gbNetParam.Controls.Add(this.label20);
            this.gbNetParam.Controls.Add(this.MaskBox);
            this.gbNetParam.Controls.Add(this.label21);
            this.gbNetParam.Controls.Add(this.label24);
            this.gbNetParam.Controls.Add(this.IPBox);
            this.gbNetParam.Controls.Add(this.MacBox);
            this.gbNetParam.Controls.Add(this.label25);
            this.gbNetParam.Controls.Add(this.label26);
            this.gbNetParam.Location = new System.Drawing.Point(384, 10);
            this.gbNetParam.Name = "gbNetParam";
            this.gbNetParam.Size = new System.Drawing.Size(406, 412);
            this.gbNetParam.TabIndex = 25;
            this.gbNetParam.TabStop = false;
            this.gbNetParam.Text = "网络参数";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label14.Location = new System.Drawing.Point(216, 320);
            this.label14.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(189, 20);
            this.label14.TabIndex = 73;
            this.label14.Text = "(0~100，单位：*10)";
            // 
            // TickBox
            // 
            this.TickBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.TickBox.Location = new System.Drawing.Point(106, 319);
            this.TickBox.Margin = new System.Windows.Forms.Padding(4);
            this.TickBox.Name = "TickBox";
            this.TickBox.Size = new System.Drawing.Size(102, 27);
            this.TickBox.TabIndex = 72;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label10.Location = new System.Drawing.Point(2, 320);
            this.label10.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(109, 20);
            this.label10.TabIndex = 71;
            this.label10.Text = "心跳时间：";
            // 
            // DNScheckBox
            // 
            this.DNScheckBox.AutoSize = true;
            this.DNScheckBox.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DNScheckBox.Location = new System.Drawing.Point(105, 344);
            this.DNScheckBox.Margin = new System.Windows.Forms.Padding(4);
            this.DNScheckBox.Name = "DNScheckBox";
            this.DNScheckBox.Size = new System.Drawing.Size(61, 24);
            this.DNScheckBox.TabIndex = 58;
            this.DNScheckBox.Text = "DNS";
            this.DNScheckBox.UseVisualStyleBackColor = true;
            // 
            // bReset
            // 
            this.bReset.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.bReset.Location = new System.Drawing.Point(166, 376);
            this.bReset.Margin = new System.Windows.Forms.Padding(4);
            this.bReset.Name = "bReset";
            this.bReset.Size = new System.Drawing.Size(74, 29);
            this.bReset.TabIndex = 70;
            this.bReset.Text = "重启";
            this.bReset.UseVisualStyleBackColor = true;
            this.bReset.Click += new System.EventHandler(this.bReset_Click);
            // 
            // bSetDefault
            // 
            this.bSetDefault.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.bSetDefault.Location = new System.Drawing.Point(302, 376);
            this.bSetDefault.Margin = new System.Windows.Forms.Padding(4);
            this.bSetDefault.Name = "bSetDefault";
            this.bSetDefault.Size = new System.Drawing.Size(80, 29);
            this.bSetDefault.TabIndex = 69;
            this.bSetDefault.Text = "恢复默认";
            this.bSetDefault.UseVisualStyleBackColor = true;
            this.bSetDefault.Visible = false;
            this.bSetDefault.Click += new System.EventHandler(this.bSetDefault_Click);
            // 
            // bSetNetParam
            // 
            this.bSetNetParam.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.bSetNetParam.Location = new System.Drawing.Point(86, 376);
            this.bSetNetParam.Margin = new System.Windows.Forms.Padding(4);
            this.bSetNetParam.Name = "bSetNetParam";
            this.bSetNetParam.Size = new System.Drawing.Size(72, 29);
            this.bSetNetParam.TabIndex = 68;
            this.bSetNetParam.Text = "设置";
            this.bSetNetParam.UseVisualStyleBackColor = true;
            this.bSetNetParam.Click += new System.EventHandler(this.bSetNetParam_Click);
            // 
            // bReadNetParam
            // 
            this.bReadNetParam.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.bReadNetParam.Location = new System.Drawing.Point(6, 376);
            this.bReadNetParam.Margin = new System.Windows.Forms.Padding(4);
            this.bReadNetParam.Name = "bReadNetParam";
            this.bReadNetParam.Size = new System.Drawing.Size(72, 29);
            this.bReadNetParam.TabIndex = 67;
            this.bReadNetParam.Text = "读取";
            this.bReadNetParam.UseVisualStyleBackColor = true;
            this.bReadNetParam.Click += new System.EventHandler(this.bReadNetParam_Click);
            // 
            // WIFIBox
            // 
            this.WIFIBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.WIFIBox.Location = new System.Drawing.Point(106, 280);
            this.WIFIBox.Margin = new System.Windows.Forms.Padding(4);
            this.WIFIBox.Name = "WIFIBox";
            this.WIFIBox.Size = new System.Drawing.Size(277, 27);
            this.WIFIBox.TabIndex = 66;
            // 
            // textBox2
            // 
            this.textBox2.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.textBox2.Location = new System.Drawing.Point(74, 240);
            this.textBox2.Margin = new System.Windows.Forms.Padding(4);
            this.textBox2.Name = "textBox2";
            this.textBox2.Size = new System.Drawing.Size(309, 27);
            this.textBox2.TabIndex = 65;
            // 
            // DNSBox
            // 
            this.DNSBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DNSBox.Location = new System.Drawing.Point(74, 200);
            this.DNSBox.Margin = new System.Windows.Forms.Padding(4);
            this.DNSBox.Name = "DNSBox";
            this.DNSBox.Size = new System.Drawing.Size(309, 27);
            this.DNSBox.TabIndex = 64;
            // 
            // DHCPcheckBox
            // 
            this.DHCPcheckBox.AutoSize = true;
            this.DHCPcheckBox.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.DHCPcheckBox.Location = new System.Drawing.Point(16, 344);
            this.DHCPcheckBox.Margin = new System.Windows.Forms.Padding(4);
            this.DHCPcheckBox.Name = "DHCPcheckBox";
            this.DHCPcheckBox.Size = new System.Drawing.Size(71, 24);
            this.DHCPcheckBox.TabIndex = 63;
            this.DHCPcheckBox.Text = "DHCP";
            this.DHCPcheckBox.UseVisualStyleBackColor = true;
            // 
            // label28
            // 
            this.label28.AutoSize = true;
            this.label28.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label28.Location = new System.Drawing.Point(9, 281);
            this.label28.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label28.Name = "label28";
            this.label28.Size = new System.Drawing.Size(109, 20);
            this.label28.TabIndex = 62;
            this.label28.Text = "WIFI密码：";
            // 
            // label27
            // 
            this.label27.AutoSize = true;
            this.label27.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label27.Location = new System.Drawing.Point(9, 244);
            this.label27.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label27.Name = "label27";
            this.label27.Size = new System.Drawing.Size(69, 20);
            this.label27.TabIndex = 61;
            this.label27.Text = "SSID：";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label29.Location = new System.Drawing.Point(9, 204);
            this.label29.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(59, 20);
            this.label29.TabIndex = 60;
            this.label29.Text = "DNS：";
            // 
            // SeverPortBox
            // 
            this.SeverPortBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SeverPortBox.Location = new System.Drawing.Point(272, 156);
            this.SeverPortBox.Margin = new System.Windows.Forms.Padding(4);
            this.SeverPortBox.Name = "SeverPortBox";
            this.SeverPortBox.Size = new System.Drawing.Size(111, 27);
            this.SeverPortBox.TabIndex = 59;
            // 
            // LocalPortBox
            // 
            this.LocalPortBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.LocalPortBox.Location = new System.Drawing.Point(272, 61);
            this.LocalPortBox.Margin = new System.Windows.Forms.Padding(4);
            this.LocalPortBox.Name = "LocalPortBox";
            this.LocalPortBox.Size = new System.Drawing.Size(111, 27);
            this.LocalPortBox.TabIndex = 58;
            // 
            // GIPBox
            // 
            this.GIPBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.GIPBox.Location = new System.Drawing.Point(74, 125);
            this.GIPBox.Margin = new System.Windows.Forms.Padding(4);
            this.GIPBox.Name = "GIPBox";
            this.GIPBox.Size = new System.Drawing.Size(178, 27);
            this.GIPBox.TabIndex = 57;
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label23.Location = new System.Drawing.Point(7, 127);
            this.label23.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(69, 20);
            this.label23.TabIndex = 56;
            this.label23.Text = "网关：";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label19.Location = new System.Drawing.Point(271, 132);
            this.label19.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(109, 20);
            this.label19.TabIndex = 55;
            this.label19.Text = "远端端口：";
            // 
            // SeverIPBox
            // 
            this.SeverIPBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.SeverIPBox.Location = new System.Drawing.Point(74, 159);
            this.SeverIPBox.Margin = new System.Windows.Forms.Padding(4);
            this.SeverIPBox.Name = "SeverIPBox";
            this.SeverIPBox.Size = new System.Drawing.Size(178, 27);
            this.SeverIPBox.TabIndex = 54;
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label20.Location = new System.Drawing.Point(-1, 167);
            this.label20.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(89, 20);
            this.label20.TabIndex = 53;
            this.label20.Text = "远程IP：";
            // 
            // MaskBox
            // 
            this.MaskBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.MaskBox.Location = new System.Drawing.Point(74, 92);
            this.MaskBox.Margin = new System.Windows.Forms.Padding(4);
            this.MaskBox.Name = "MaskBox";
            this.MaskBox.Size = new System.Drawing.Size(180, 27);
            this.MaskBox.TabIndex = 52;
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label21.Location = new System.Drawing.Point(7, 93);
            this.label21.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(69, 20);
            this.label21.TabIndex = 51;
            this.label21.Text = "掩码：";
            // 
            // label24
            // 
            this.label24.AutoSize = true;
            this.label24.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label24.Location = new System.Drawing.Point(268, 33);
            this.label24.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label24.Name = "label24";
            this.label24.Size = new System.Drawing.Size(109, 20);
            this.label24.TabIndex = 50;
            this.label24.Text = "本地端口：";
            // 
            // IPBox
            // 
            this.IPBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.IPBox.Location = new System.Drawing.Point(74, 57);
            this.IPBox.Margin = new System.Windows.Forms.Padding(4);
            this.IPBox.Name = "IPBox";
            this.IPBox.Size = new System.Drawing.Size(180, 27);
            this.IPBox.TabIndex = 49;
            // 
            // MacBox
            // 
            this.MacBox.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.MacBox.Location = new System.Drawing.Point(72, 23);
            this.MacBox.Margin = new System.Windows.Forms.Padding(4);
            this.MacBox.Name = "MacBox";
            this.MacBox.Size = new System.Drawing.Size(182, 27);
            this.MacBox.TabIndex = 46;
            // 
            // label25
            // 
            this.label25.AutoSize = true;
            this.label25.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label25.Location = new System.Drawing.Point(-1, 62);
            this.label25.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label25.Name = "label25";
            this.label25.Size = new System.Drawing.Size(89, 20);
            this.label25.TabIndex = 48;
            this.label25.Text = "IP地址：";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label26.Location = new System.Drawing.Point(7, 26);
            this.label26.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(59, 20);
            this.label26.TabIndex = 47;
            this.label26.Text = "MAC：";
            // 
            // gbSysParam
            // 
            this.gbSysParam.Controls.Add(this.label15);
            this.gbSysParam.Controls.Add(this.cbList);
            this.gbSysParam.Controls.Add(this.label22);
            this.gbSysParam.Controls.Add(this.cbPortMode);
            this.gbSysParam.Controls.Add(this.cbBuz);
            this.gbSysParam.Controls.Add(this.tbTransMode);
            this.gbSysParam.Controls.Add(this.tbAddr);
            this.gbSysParam.Controls.Add(this.tbPower);
            this.gbSysParam.Controls.Add(this.tbFilterTime);
            this.gbSysParam.Controls.Add(this.label11);
            this.gbSysParam.Controls.Add(this.label12);
            this.gbSysParam.Controls.Add(this.label13);
            this.gbSysParam.Controls.Add(this.label17);
            this.gbSysParam.Controls.Add(this.label18);
            this.gbSysParam.Controls.Add(this.bReadDevice);
            this.gbSysParam.Controls.Add(this.bSetDevice);
            this.gbSysParam.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbSysParam.Location = new System.Drawing.Point(1, 5);
            this.gbSysParam.Margin = new System.Windows.Forms.Padding(4);
            this.gbSysParam.Name = "gbSysParam";
            this.gbSysParam.Padding = new System.Windows.Forms.Padding(4);
            this.gbSysParam.Size = new System.Drawing.Size(366, 252);
            this.gbSysParam.TabIndex = 24;
            this.gbSysParam.TabStop = false;
            this.gbSysParam.Text = "设备Device参数";
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(225, 103);
            this.label15.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(98, 18);
            this.label15.TabIndex = 41;
            this.label15.Text = "（0~31级）";
            // 
            // cbList
            // 
            this.cbList.AutoSize = true;
            this.cbList.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cbList.Location = new System.Drawing.Point(228, 138);
            this.cbList.Margin = new System.Windows.Forms.Padding(4);
            this.cbList.Name = "cbList";
            this.cbList.Size = new System.Drawing.Size(131, 24);
            this.cbList.TabIndex = 39;
            this.cbList.Text = "打开白名单";
            this.cbList.UseVisualStyleBackColor = true;
            this.cbList.Visible = false;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(234, 215);
            this.label22.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(53, 18);
            this.label22.TabIndex = 38;
            this.label22.Text = "状态:";
            // 
            // cbPortMode
            // 
            this.cbPortMode.FormattingEnabled = true;
            this.cbPortMode.Items.AddRange(new object[] {
            "USB",
            "TCP",
            "Wifi",
            "RS232",
            "TCPJson",
            "RS485",
            "GPRS",
            "UDPJson"});
            this.cbPortMode.Location = new System.Drawing.Point(98, 28);
            this.cbPortMode.Margin = new System.Windows.Forms.Padding(4);
            this.cbPortMode.Name = "cbPortMode";
            this.cbPortMode.Size = new System.Drawing.Size(119, 25);
            this.cbPortMode.TabIndex = 37;
            // 
            // cbBuz
            // 
            this.cbBuz.AutoSize = true;
            this.cbBuz.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cbBuz.Location = new System.Drawing.Point(228, 170);
            this.cbBuz.Margin = new System.Windows.Forms.Padding(4);
            this.cbBuz.Name = "cbBuz";
            this.cbBuz.Size = new System.Drawing.Size(131, 24);
            this.cbBuz.TabIndex = 31;
            this.cbBuz.Text = "蜂鸣器选择";
            this.cbBuz.UseVisualStyleBackColor = true;
            // 
            // tbTransMode
            // 
            this.tbTransMode.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tbTransMode.Location = new System.Drawing.Point(98, 133);
            this.tbTransMode.Margin = new System.Windows.Forms.Padding(4);
            this.tbTransMode.Name = "tbTransMode";
            this.tbTransMode.Size = new System.Drawing.Size(119, 27);
            this.tbTransMode.TabIndex = 29;
            // 
            // tbAddr
            // 
            this.tbAddr.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tbAddr.Location = new System.Drawing.Point(98, 172);
            this.tbAddr.Margin = new System.Windows.Forms.Padding(4);
            this.tbAddr.Name = "tbAddr";
            this.tbAddr.Size = new System.Drawing.Size(119, 27);
            this.tbAddr.TabIndex = 28;
            // 
            // tbPower
            // 
            this.tbPower.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tbPower.Location = new System.Drawing.Point(98, 98);
            this.tbPower.Margin = new System.Windows.Forms.Padding(4);
            this.tbPower.Name = "tbPower";
            this.tbPower.Size = new System.Drawing.Size(119, 27);
            this.tbPower.TabIndex = 27;
            // 
            // tbFilterTime
            // 
            this.tbFilterTime.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tbFilterTime.Location = new System.Drawing.Point(98, 63);
            this.tbFilterTime.Margin = new System.Windows.Forms.Padding(4);
            this.tbFilterTime.Name = "tbFilterTime";
            this.tbFilterTime.Size = new System.Drawing.Size(119, 27);
            this.tbFilterTime.TabIndex = 26;
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label11.Location = new System.Drawing.Point(3, 137);
            this.label11.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(109, 20);
            this.label11.TabIndex = 15;
            this.label11.Text = "传输模式：";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label12.Location = new System.Drawing.Point(3, 173);
            this.label12.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(109, 20);
            this.label12.TabIndex = 14;
            this.label12.Text = "设备地址：";
            // 
            // label13
            // 
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label13.Location = new System.Drawing.Point(3, 99);
            this.label13.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(109, 20);
            this.label13.TabIndex = 13;
            this.label13.Text = "射频功率：";
            // 
            // label17
            // 
            this.label17.AutoSize = true;
            this.label17.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label17.Location = new System.Drawing.Point(3, 62);
            this.label17.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label17.Name = "label17";
            this.label17.Size = new System.Drawing.Size(109, 20);
            this.label17.TabIndex = 8;
            this.label17.Text = "过滤时间：";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label18.Location = new System.Drawing.Point(3, 28);
            this.label18.Margin = new System.Windows.Forms.Padding(4, 0, 4, 0);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(109, 20);
            this.label18.TabIndex = 12;
            this.label18.Text = "上传接口：";
            // 
            // bReadDevice
            // 
            this.bReadDevice.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.bReadDevice.Location = new System.Drawing.Point(15, 209);
            this.bReadDevice.Margin = new System.Windows.Forms.Padding(4);
            this.bReadDevice.Name = "bReadDevice";
            this.bReadDevice.Size = new System.Drawing.Size(98, 29);
            this.bReadDevice.TabIndex = 4;
            this.bReadDevice.Text = "读取";
            this.bReadDevice.UseVisualStyleBackColor = true;
            this.bReadDevice.Click += new System.EventHandler(this.bReadDevice_Click);
            // 
            // bSetDevice
            // 
            this.bSetDevice.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.bSetDevice.Location = new System.Drawing.Point(121, 209);
            this.bSetDevice.Margin = new System.Windows.Forms.Padding(4);
            this.bSetDevice.Name = "bSetDevice";
            this.bSetDevice.Size = new System.Drawing.Size(105, 29);
            this.bSetDevice.TabIndex = 6;
            this.bSetDevice.Text = "设置";
            this.bSetDevice.UseVisualStyleBackColor = true;
            this.bSetDevice.Click += new System.EventHandler(this.bSetDevice_Click);
            // 
            // timer1
            // 
            this.timer1.Interval = 200;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // ViewTime
            // 
            this.ViewTime.Interval = 500;
            this.ViewTime.Tick += new System.EventHandler(this.ViewTime_Tick);
            // 
            // panel7
            // 
            this.panel7.Location = new System.Drawing.Point(11, 223);
            this.panel7.Name = "panel7";
            this.panel7.Size = new System.Drawing.Size(304, 48);
            this.panel7.TabIndex = 5;
            // 
            // groupBox6
            // 
            this.groupBox6.Controls.Add(this.ConnectButton);
            this.groupBox6.Controls.Add(this.ScanButton);
            this.groupBox6.Location = new System.Drawing.Point(14, 218);
            this.groupBox6.Name = "groupBox6";
            this.groupBox6.Size = new System.Drawing.Size(300, 45);
            this.groupBox6.TabIndex = 0;
            this.groupBox6.TabStop = false;
            // 
            // ConnectButton
            // 
            this.ConnectButton.BackColor = System.Drawing.SystemColors.ControlLight;
            this.ConnectButton.Font = new System.Drawing.Font("宋体", 10.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ConnectButton.Location = new System.Drawing.Point(140, 9);
            this.ConnectButton.Name = "ConnectButton";
            this.ConnectButton.Size = new System.Drawing.Size(85, 36);
            this.ConnectButton.TabIndex = 7;
            this.ConnectButton.Text = "连接";
            this.ConnectButton.UseVisualStyleBackColor = false;
            this.ConnectButton.Click += new System.EventHandler(this.ConnectButton_Click);
            // 
            // ScanButton
            // 
            this.ScanButton.BackColor = System.Drawing.SystemColors.ControlLight;
            this.ScanButton.Font = new System.Drawing.Font("宋体", 10.8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.ScanButton.Location = new System.Drawing.Point(22, 9);
            this.ScanButton.Name = "ScanButton";
            this.ScanButton.Size = new System.Drawing.Size(85, 36);
            this.ScanButton.TabIndex = 6;
            this.ScanButton.Text = "扫描";
            this.ScanButton.UseVisualStyleBackColor = false;
            this.ScanButton.Click += new System.EventHandler(this.ScanButton_Click_1);
            // 
            // columnHeader1
            // 
            this.columnHeader1.DisplayIndex = 0;
            this.columnHeader1.Text = "序号";
            // 
            // columnHeader2
            // 
            this.columnHeader2.DisplayIndex = 1;
            this.columnHeader2.Text = "标签ID";
            this.columnHeader2.Width = 180;
            // 
            // columnHeader3
            // 
            this.columnHeader3.DisplayIndex = 2;
            this.columnHeader3.Text = "Rssi";
            this.columnHeader3.Width = 80;
            // 
            // columnHeader4
            // 
            this.columnHeader4.DisplayIndex = 3;
            this.columnHeader4.Text = "状态字";
            this.columnHeader4.Width = 80;
            // 
            // columnHeader5
            // 
            this.columnHeader5.DisplayIndex = 4;
            this.columnHeader5.Text = "标签类型";
            this.columnHeader5.Width = 120;
            // 
            // columnHeader6
            // 
            this.columnHeader6.DisplayIndex = 5;
            this.columnHeader6.Text = "次数";
            this.columnHeader6.Width = 80;
            // 
            // columnHeader7
            // 
            this.columnHeader7.DisplayIndex = 6;
            this.columnHeader7.Text = "时间";
            this.columnHeader7.Width = 180;
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(10F, 20F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1142, 716);
            this.Controls.Add(this.gUART);
            this.Controls.Add(this.gTCPClient);
            this.Controls.Add(this.gTcpSever);
            this.Controls.Add(this.groupBox6);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.panel7);
            this.Controls.Add(this.panel6);
            this.Controls.Add(this.panel2);
            this.Controls.Add(this.panel4);
            this.Controls.Add(this.panel3);
            this.Controls.Add(this.panel1);
            this.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Margin = new System.Windows.Forms.Padding(4);
            this.Name = "Form1";
            this.Text = "CallTagDemo";
            this.Load += new System.EventHandler(this.Form1_Load);
            this.panel1.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.gUSB.ResumeLayout(false);
            this.gUSB.PerformLayout();
            this.gTCPClient.ResumeLayout(false);
            this.gTCPClient.PerformLayout();
            this.gUART.ResumeLayout(false);
            this.gUART.PerformLayout();
            this.gTcpSever.ResumeLayout(false);
            this.gTcpSever.PerformLayout();
            this.panel3.ResumeLayout(false);
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox4.ResumeLayout(false);
            this.gList.ResumeLayout(false);
            this.gList.PerformLayout();
            this.groupBox5.ResumeLayout(false);
            this.groupBox5.PerformLayout();
            this.groupBox2.ResumeLayout(false);
            this.panel6.ResumeLayout(false);
            this.panel4.ResumeLayout(false);
            this.tabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.gbNetParam.ResumeLayout(false);
            this.gbNetParam.PerformLayout();
            this.gbSysParam.ResumeLayout(false);
            this.gbSysParam.PerformLayout();
            this.groupBox6.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel panel3;
        private System.Windows.Forms.Panel panel5;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.GroupBox gUSB;
        private System.Windows.Forms.GroupBox groupBox3;
        private System.Windows.Forms.TextBox MesBox;
        private System.Windows.Forms.GroupBox groupBox4;
        private System.Windows.Forms.Panel panel6;
        private System.Windows.Forms.Panel panel4;
        private System.Windows.Forms.RadioButton rdTCPSever;
        private System.Windows.Forms.RadioButton rdUart;
        private System.Windows.Forms.RadioButton rdUSB;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.RadioButton rdTCPClient;
        private System.Windows.Forms.GroupBox gTcpSever;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.GroupBox gUART;
        private System.Windows.Forms.ComboBox COMBox;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.GroupBox gTCPClient;
        private System.Windows.Forms.TextBox CportBox;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox CIpBox;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.GroupBox groupBox5;
        private System.Windows.Forms.TextBox FindIdBox;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Button StopFindbutton;
        private System.Windows.Forms.Button Findbutton;
        private System.Windows.Forms.GroupBox groupBox2;
        private System.Windows.Forms.Button StopInquiryButton;
        private System.Windows.Forms.Button InquiryButton;
        private System.Windows.Forms.TextBox SPortBox;
        private System.Windows.Forms.Timer timer1;
        private System.Windows.Forms.Button Clearbutton;
        private System.Windows.Forms.Button button1;
        private System.Windows.Forms.Timer ViewTime;
        private System.Windows.Forms.Label TagNumlabel;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.ComboBox comboBox3;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.CheckBox checkBuz;
        private System.Windows.Forms.ComboBox comboBox2;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.GroupBox gList;
        private System.Windows.Forms.Button DataOutBut;
        private System.Windows.Forms.Button ReorderBut;
        private System.Windows.Forms.RadioButton radioHex;
        private System.Windows.Forms.RadioButton radioDec;
        private System.Windows.Forms.Panel panel7;
        private System.Windows.Forms.GroupBox groupBox6;
        private System.Windows.Forms.Button ScanButton;
        private System.Windows.Forms.Button ConnectButton;
        private System.Windows.Forms.ListBox jytConn;
        private System.Windows.Forms.ComboBox cbUSB;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private System.Windows.Forms.TabPage tabPage2;
        private System.Windows.Forms.GroupBox gbNetParam;
        private System.Windows.Forms.Button bReset;
        private System.Windows.Forms.Button bSetDefault;
        private System.Windows.Forms.Button bSetNetParam;
        private System.Windows.Forms.Button bReadNetParam;
        private System.Windows.Forms.TextBox WIFIBox;
        private System.Windows.Forms.TextBox textBox2;
        private System.Windows.Forms.TextBox DNSBox;
        private System.Windows.Forms.CheckBox DHCPcheckBox;
        private System.Windows.Forms.Label label28;
        private System.Windows.Forms.Label label27;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.TextBox SeverPortBox;
        private System.Windows.Forms.TextBox LocalPortBox;
        private System.Windows.Forms.TextBox GIPBox;
        private System.Windows.Forms.Label label23;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.TextBox SeverIPBox;
        private System.Windows.Forms.Label label20;
        private System.Windows.Forms.TextBox MaskBox;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.Label label24;
        private System.Windows.Forms.TextBox IPBox;
        private System.Windows.Forms.TextBox MacBox;
        private System.Windows.Forms.Label label25;
        private System.Windows.Forms.Label label26;
        private System.Windows.Forms.GroupBox gbSysParam;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.CheckBox cbList;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.ComboBox cbPortMode;
        private System.Windows.Forms.CheckBox cbBuz;
        private System.Windows.Forms.TextBox tbTransMode;
        private System.Windows.Forms.TextBox tbAddr;
        private System.Windows.Forms.TextBox tbPower;
        private System.Windows.Forms.TextBox tbFilterTime;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label17;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Button bReadDevice;
        private System.Windows.Forms.Button bSetDevice;
        private System.Windows.Forms.ColumnHeader columnHeader1;
        private System.Windows.Forms.ColumnHeader columnHeader2;
        private System.Windows.Forms.ColumnHeader columnHeader3;
        private System.Windows.Forms.ColumnHeader columnHeader4;
        private System.Windows.Forms.ColumnHeader columnHeader5;
        private System.Windows.Forms.ColumnHeader columnHeader6;
        private System.Windows.Forms.ColumnHeader columnHeader7;
        private System.Windows.Forms.ListView IvTagData;
        private System.Windows.Forms.ColumnHeader columnHeader8;
        private System.Windows.Forms.ColumnHeader columnHeader9;
        private System.Windows.Forms.ColumnHeader columnHeader10;
        private System.Windows.Forms.ColumnHeader columnHeader11;
        private System.Windows.Forms.ColumnHeader columnHeader12;
        private System.Windows.Forms.ColumnHeader columnHeader13;
        private System.Windows.Forms.ColumnHeader columnHeader14;
        private System.Windows.Forms.CheckBox DNScheckBox;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.TextBox TickBox;
        private System.Windows.Forms.Label label10;
    }
}

