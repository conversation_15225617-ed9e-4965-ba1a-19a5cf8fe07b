#!/bin/bash
# Installation script for CallTag Controller on Linux
# Linux系统安装脚本

set -e  # Exit on any error

echo "CallTag Controller - Linux Installation Script"
echo "=============================================="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   echo "This script should not be run as root for safety reasons."
   echo "Please run as a regular user. Sudo will be used when needed."
   exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check Python installation
echo "Checking Python installation..."
if command_exists python3; then
    PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    echo "Found Python $PYTHON_VERSION"
    
    # Check if version is 3.6 or higher
    PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [[ $PYTHON_MAJOR -lt 3 ]] || [[ $PYTHON_MAJOR -eq 3 && $PYTHON_MINOR -lt 6 ]]; then
        echo "Error: Python 3.6 or higher is required. Found $PYTHON_VERSION"
        exit 1
    fi
else
    echo "Error: Python 3 is not installed."
    echo "Please install Python 3.6 or higher:"
    echo "  Ubuntu/Debian: sudo apt update && sudo apt install python3 python3-pip"
    echo "  CentOS/RHEL:   sudo yum install python3 python3-pip"
    echo "  Fedora:        sudo dnf install python3 python3-pip"
    echo "  Arch:          sudo pacman -S python python-pip"
    exit 1
fi

# Check pip installation
echo "Checking pip installation..."
if ! command_exists pip3; then
    echo "pip3 not found. Installing pip..."
    if command_exists apt; then
        sudo apt update && sudo apt install python3-pip
    elif command_exists yum; then
        sudo yum install python3-pip
    elif command_exists dnf; then
        sudo dnf install python3-pip
    elif command_exists pacman; then
        sudo pacman -S python-pip
    else
        echo "Could not install pip automatically. Please install it manually."
        exit 1
    fi
fi

# Install Python dependencies
echo "Installing Python dependencies..."
pip3 install --user pyserial hidapi

# Set up serial port permissions
echo "Setting up serial port permissions..."
if groups $USER | grep -q dialout; then
    echo "User $USER is already in dialout group"
else
    echo "Adding user $USER to dialout group..."
    sudo usermod -a -G dialout $USER
    echo "You will need to log out and log back in for group changes to take effect."
fi

# Set up USB device permissions
echo "Setting up USB device permissions..."
UDEV_RULE_FILE="/etc/udev/rules.d/99-calltag.rules"

if [[ -f "$UDEV_RULE_FILE" ]]; then
    echo "USB udev rules already exist"
else
    echo "Creating USB udev rules..."
    sudo tee "$UDEV_RULE_FILE" > /dev/null << 'EOF'
# CallTag USB HID device rules
# Allow access to CallTag devices for all users
SUBSYSTEM=="hidraw", ATTRS{idVendor}=="0483", ATTRS{idProduct}=="5750", MODE="0666", GROUP="plugdev"
SUBSYSTEM=="usb", ATTRS{idVendor}=="0483", ATTRS{idProduct}=="5750", MODE="0666", GROUP="plugdev"

# Generic HID device access (uncomment if needed)
# SUBSYSTEM=="hidraw", MODE="0666", GROUP="plugdev"
EOF
    
    echo "Reloading udev rules..."
    sudo udevadm control --reload-rules
    sudo udevadm trigger
fi

# Add user to plugdev group
if groups $USER | grep -q plugdev; then
    echo "User $USER is already in plugdev group"
else
    echo "Adding user $USER to plugdev group..."
    sudo usermod -a -G plugdev $USER
fi

# Create installation directory
INSTALL_DIR="$HOME/calltag_controller"
echo "Installing CallTag Controller to $INSTALL_DIR..."

if [[ -d "$INSTALL_DIR" ]]; then
    echo "Directory already exists. Backing up..."
    mv "$INSTALL_DIR" "$INSTALL_DIR.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Copy files
mkdir -p "$INSTALL_DIR"
cp -r calltag_controller "$INSTALL_DIR/"
cp *.py "$INSTALL_DIR/"
cp README.md "$INSTALL_DIR/"
cp requirements.txt "$INSTALL_DIR/"

# Make scripts executable
chmod +x "$INSTALL_DIR"/*.py

# Create symlinks in user's local bin
LOCAL_BIN="$HOME/.local/bin"
mkdir -p "$LOCAL_BIN"

echo "Creating command shortcuts..."
ln -sf "$INSTALL_DIR/linux_usage_example.py" "$LOCAL_BIN/calltag-manager"
ln -sf "$INSTALL_DIR/test_calltag.py" "$LOCAL_BIN/calltag-test"

# Add local bin to PATH if not already there
if [[ ":$PATH:" != *":$LOCAL_BIN:"* ]]; then
    echo "Adding $LOCAL_BIN to PATH..."
    echo 'export PATH="$HOME/.local/bin:$PATH"' >> "$HOME/.bashrc"
    echo "Please run 'source ~/.bashrc' or restart your terminal to update PATH"
fi

# Create desktop entry (optional)
DESKTOP_DIR="$HOME/.local/share/applications"
if [[ -d "$DESKTOP_DIR" ]]; then
    echo "Creating desktop entry..."
    cat > "$DESKTOP_DIR/calltag-manager.desktop" << EOF
[Desktop Entry]
Name=CallTag Manager
Comment=Manage CallTag sound-light readers
Exec=$LOCAL_BIN/calltag-manager
Icon=applications-electronics
Terminal=true
Type=Application
Categories=System;Electronics;
EOF
fi

# Test installation
echo "Testing installation..."
cd "$INSTALL_DIR"
if python3 test_calltag.py; then
    echo "Installation test passed!"
else
    echo "Installation test failed!"
    exit 1
fi

echo ""
echo "Installation completed successfully!"
echo ""
echo "Usage:"
echo "  calltag-manager    - Start the CallTag manager"
echo "  calltag-test       - Run tests"
echo ""
echo "Or run directly:"
echo "  cd $INSTALL_DIR"
echo "  python3 linux_usage_example.py"
echo ""
echo "Important notes:"
echo "1. If you were added to groups, please log out and log back in"
echo "2. For serial devices, use ports like /dev/ttyUSB0, /dev/ttyACM0"
echo "3. For USB devices, make sure the device is connected"
echo "4. Check 'dmesg' output when connecting devices"
echo ""
echo "Troubleshooting:"
echo "  - Check device permissions: ls -l /dev/ttyUSB*"
echo "  - Check USB devices: lsusb"
echo "  - Check groups: groups \$USER"
echo "  - Check logs: dmesg | tail"
