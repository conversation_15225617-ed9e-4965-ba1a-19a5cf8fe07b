#!/usr/bin/env python3
"""
TCP Client demo for CallTag Controller
TCP客户端演示
"""

import time
import sys
import os

# Add parent directory to path to import calltag_controller
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from calltag_controller import CallTagDevice, TCPClientConnection
from calltag_controller.core.protocol import Protocol


class CallTagClient:
    """
    CallTag TCP Client implementation
    声光标签TCP客户端实现
    """
    
    def __init__(self, host='*************', port=60000):
        self.connection = TCPClientConnection(host, port)
        self.device = CallTagDevice(self.connection)
        
        # Set up callbacks
        self.device.on_tag_detected = self.on_tag_detected
        self.device.on_error = self.on_error
        self.device.on_connection_lost = self.on_connection_lost
    
    def connect(self):
        """Connect to the server"""
        print(f"Connecting to {self.connection.host}:{self.connection.port}...")
        if self.device.connect():
            print("Connected successfully!")
            return True
        else:
            print("Failed to connect")
            return False
    
    def disconnect(self):
        """Disconnect from the server"""
        print("Disconnecting...")
        self.device.disconnect()
        print("Disconnected")
    
    def on_tag_detected(self, tag_info):
        """Called when a tag is detected"""
        print(f"Tag detected: {tag_info}")
    
    def on_error(self, error_msg):
        """Called when an error occurs"""
        print(f"Error: {error_msg}")
    
    def on_connection_lost(self):
        """Called when connection is lost"""
        print("Connection lost!")
    
    def start_reading(self):
        """Start reading tags"""
        print("Starting to read tags...")
        if self.device.start_reading():
            print("Reading started successfully")
            return True
        else:
            print("Failed to start reading")
            return False
    
    def stop_reading(self):
        """Stop reading tags"""
        print("Stopping reading...")
        self.device.stop_reading()
        print("Reading stopped")
    
    def call_tag(self, tag_id, led_mode=Protocol.LED_FLASH_MODE, time_mode=Protocol.TIME_30_SEC, buzzer=True):
        """Call a specific tag"""
        print(f"Calling tag {tag_id}...")
        if self.device.call_tag(tag_id, led_mode, time_mode, buzzer):
            print(f"Call command sent for tag {tag_id}")
            return True
        else:
            print(f"Failed to call tag {tag_id}")
            return False
    
    def stop_call_tag(self, tag_id):
        """Stop calling a specific tag"""
        print(f"Stopping call for tag {tag_id}...")
        if self.device.stop_call_tag(tag_id):
            print(f"Stop call command sent for tag {tag_id}")
            return True
        else:
            print(f"Failed to stop calling tag {tag_id}")
            return False
    
    def show_cached_tags(self):
        """Show all cached tags"""
        tags = self.device.get_cached_tags()
        print(f"\nCached tags ({len(tags)}):")
        if tags:
            for tag in tags:
                print(f"  {tag}")
        else:
            print("  No tags found")
        print()
    
    def clear_cache(self):
        """Clear tag cache"""
        self.device.clear_cache()
        print("Tag cache cleared")


def interactive_mode(client):
    """Interactive mode for testing"""
    print("\nInteractive mode. Available commands:")
    print("  'start'           - Start reading tags")
    print("  'stop'            - Stop reading tags")
    print("  'call <id>'       - Call tag with specified ID")
    print("  'stopcall <id>'   - Stop calling tag with specified ID")
    print("  'tags'            - Show cached tags")
    print("  'clear'           - Clear tag cache")
    print("  'info'            - Show device info")
    print("  'quit'            - Exit")
    print()
    
    while True:
        try:
            cmd = input("Client> ").strip().split()
            
            if not cmd:
                continue
            
            command = cmd[0].lower()
            
            if command == 'quit':
                break
            elif command == 'start':
                client.start_reading()
            elif command == 'stop':
                client.stop_reading()
            elif command == 'call':
                if len(cmd) > 1:
                    try:
                        tag_id = int(cmd[1])
                        client.call_tag(tag_id)
                    except ValueError:
                        print("Invalid tag ID. Please enter a number.")
                else:
                    print("Usage: call <tag_id>")
            elif command == 'stopcall':
                if len(cmd) > 1:
                    try:
                        tag_id = int(cmd[1])
                        client.stop_call_tag(tag_id)
                    except ValueError:
                        print("Invalid tag ID. Please enter a number.")
                else:
                    print("Usage: stopcall <tag_id>")
            elif command == 'tags':
                client.show_cached_tags()
            elif command == 'clear':
                client.clear_cache()
            elif command == 'info':
                info = client.device.get_device_info()
                print(f"Device info: {info}")
            elif command == 'help':
                print("Available commands: start, stop, call, stopcall, tags, clear, info, quit")
            else:
                print(f"Unknown command: {command}")
        
        except EOFError:
            break
        except KeyboardInterrupt:
            print("\nUse 'quit' to exit")


def demo_sequence(client):
    """Run a demonstration sequence"""
    print("\n=== Running Demo Sequence ===")
    
    # Start reading
    if not client.start_reading():
        return
    
    print("Reading tags for 10 seconds...")
    time.sleep(10)
    
    # Show what we found
    client.show_cached_tags()
    
    # Call a tag
    print("Calling tag 123456...")
    client.call_tag(123456, Protocol.LED_FLASH_MODE, Protocol.TIME_30_SEC, True)
    
    # Wait a bit
    time.sleep(5)
    
    # Stop calling
    print("Stopping call...")
    client.stop_call_tag(123456)
    
    # Continue reading for a bit more
    print("Reading for 5 more seconds...")
    time.sleep(5)
    
    # Stop reading
    client.stop_reading()
    
    # Final tag count
    client.show_cached_tags()
    
    print("Demo sequence completed")


def main():
    """Main function"""
    print("CallTag TCP Client Demo")
    
    # Get connection parameters
    host = input("Enter server IP (default: *************): ").strip()
    if not host:
        host = "*************"
    
    port_str = input("Enter server port (default: 60000): ").strip()
    if not port_str:
        port = 60000
    else:
        try:
            port = int(port_str)
        except ValueError:
            print("Invalid port number, using default 60000")
            port = 60000
    
    # Create client
    client = CallTagClient(host, port)
    
    try:
        # Connect
        if not client.connect():
            return
        
        # Choose mode
        print("\nChoose mode:")
        print("1. Demo sequence")
        print("2. Interactive mode")
        
        choice = input("Enter choice (1-2): ").strip()
        
        if choice == '1':
            demo_sequence(client)
        elif choice == '2':
            interactive_mode(client)
        else:
            print("Invalid choice")
    
    except KeyboardInterrupt:
        print("\nShutting down...")
    
    finally:
        client.disconnect()


if __name__ == '__main__':
    main()
